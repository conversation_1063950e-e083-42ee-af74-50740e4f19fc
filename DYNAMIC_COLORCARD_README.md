# Dynamic ColorCard Component

A flexible and reusable Vue 3 wallet card component built from the original ColorCard.vue. This component provides dynamic content, customizable backgrounds, and interactive features for displaying wallet/card information.

## Features

- ✅ **Dynamic Background Selection** - Choose from bg-1, bg-2, bg-3, bg-4 backgrounds
- ✅ **Dynamic Content** - Configurable wallet name, amount, and card number
- ✅ **Interactive Features** - Clickable cards with hover and focus events
- ✅ **Custom Styling** - Customizable CSS classes for all elements
- ✅ **Icon Customization** - Custom icons or use default mastercard-style icon
- ✅ **Slot Support** - Custom content rendering through named slots
- ✅ **Accessibility** - ARIA labels and keyboard navigation support
- ✅ **Event System** - Comprehensive event emissions for interactions
- ✅ **Card Number Masking** - Automatic masking of card numbers for security
- ✅ **Currency Formatting** - Automatic currency formatting for amounts

## Basic Usage

```vue
<template>
  <ColorCard
    wallet-name="Main Wallet"
    :amount="48200.00"
    card-number="6219 8610 2888 8075"
    background="bg-1"
    :clickable="true"
    @click="handleCardClick"
  />
</template>

<script setup>
import ColorCard from '@/components/shared/ColorCard.vue'

function handleCardClick(cardData) {
  console.log('Card clicked:', cardData)
}
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `walletName` | String | `'Main Wallet'` | Display name for the wallet |
| `amount` | String/Number | `'$48,200.00'` | Wallet amount (auto-formatted if number) |
| `cardNumber` | String | `'6219 8610 2888 8075'` | Card number (auto-masked) |
| `background` | String | `'bg-1'` | Background style: 'bg-1', 'bg-2', 'bg-3', 'bg-4' |
| `cardClass` | String | `''` | Additional CSS classes for the card |
| `walletNameClass` | String | `'f12-medium text-GrayDark'` | CSS classes for wallet name |
| `amountClass` | String | `'label-01'` | CSS classes for amount |
| `cardNumberClass` | String | `'f12-medium'` | CSS classes for card number |
| `showIcon` | Boolean | `true` | Show/hide the card icon |
| `customIcon` | String | `null` | Custom HTML/SVG for icon |
| `clickable` | Boolean | `false` | Enable click interactions |
| `disabled` | Boolean | `false` | Disable all interactions |

## Background Options

The component supports four predefined background styles:

- **bg-1**: First background image (my-cart-item-1.png)
- **bg-2**: Second background image (my-cart-item-2.png)  
- **bg-3**: Third background image (my-cart-item-3.png)
- **bg-4**: Fourth background image (my-cart-item-4.png)

```vue
<!-- Different background examples -->
<ColorCard background="bg-1" wallet-name="Premium Account" />
<ColorCard background="bg-2" wallet-name="Business Wallet" />
<ColorCard background="bg-3" wallet-name="Savings Account" />
<ColorCard background="bg-4" wallet-name="Travel Fund" />
```

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `click` | `{ walletName, amount, cardNumber, background, event }` | Fired when card is clicked |
| `hover` | `{ type, walletName, event }` | Fired on mouse enter/leave |
| `focus` | `{ walletName, event }` | Fired when card receives focus |

## Custom Content with Slots

### Wallet Name Slot
```vue
<ColorCard>
  <template #walletName="{ walletName, background }">
    <div class="custom-wallet-name">
      <i class="icon-wallet"></i>
      {{ walletName }}
    </div>
  </template>
</ColorCard>
```

### Amount Slot
```vue
<ColorCard>
  <template #amount="{ amount, formattedAmount, background }">
    <div class="custom-amount">
      {{ formattedAmount }}
      <small class="growth-indicator">+12.5%</small>
    </div>
  </template>
</ColorCard>
```

### Card Number Slot
```vue
<ColorCard>
  <template #cardNumber="{ cardNumber, maskedCardNumber, background }">
    <div class="crypto-address">
      {{ cardNumber.substring(0, 8) }}...{{ cardNumber.substring(cardNumber.length - 8) }}
    </div>
  </template>
</ColorCard>
```

### Icon Slot
```vue
<ColorCard>
  <template #icon="{ background, walletName }">
    <div class="custom-icon">
      <i class="icon-star"></i>
    </div>
  </template>
</ColorCard>
```

### Additional Content Slot
```vue
<ColorCard>
  <template #additional="{ walletName, amount, cardNumber, background }">
    <div class="extra-info">
      <small>APY: 2.5%</small>
    </div>
  </template>
</ColorCard>
```

## Advanced Examples

### Interactive Premium Card
```vue
<ColorCard
  wallet-name="Premium Account"
  :amount="125000.50"
  card-number="4532 1234 5678 9012"
  background="bg-1"
  :clickable="true"
  card-class="premium-card"
  @click="handlePremiumClick"
  @hover="handlePremiumHover"
>
  <template #icon>
    <div class="premium-icon">
      <i class="icon-star"></i>
    </div>
  </template>
</ColorCard>
```

### Business Card with Custom Content
```vue
<ColorCard
  wallet-name="Business Wallet"
  :amount="75000"
  card-number="5555 4444 3333 2222"
  background="bg-2"
  :clickable="true"
>
  <template #walletName="{ walletName }">
    <div class="business-header">
      <i class="icon-briefcase"></i>
      {{ walletName }}
    </div>
  </template>
  
  <template #amount="{ formattedAmount }">
    <div class="business-amount">
      {{ formattedAmount }}
      <small class="growth">+12.5%</small>
    </div>
  </template>
  
  <template #additional>
    <div class="business-info">
      <small>Tax Year: 2024</small>
    </div>
  </template>
</ColorCard>
```

### Crypto Wallet Card
```vue
<ColorCard
  wallet-name="Crypto Wallet"
  amount="₿ 2.5847"
  card-number="******************************************"
  background="bg-4"
  :clickable="true"
>
  <template #icon>
    <div class="crypto-icon">
      <!-- Bitcoin SVG icon -->
    </div>
  </template>
  
  <template #cardNumber="{ cardNumber }">
    <div class="crypto-address">
      {{ cardNumber.substring(0, 8) }}...{{ cardNumber.substring(cardNumber.length - 8) }}
    </div>
  </template>
</ColorCard>
```

### Disabled/Non-interactive Cards
```vue
<!-- Disabled card -->
<ColorCard
  wallet-name="Locked Account"
  amount="$0.00"
  card-number="**** **** **** ****"
  background="bg-2"
  :disabled="true"
  :clickable="true"
/>

<!-- Display-only card -->
<ColorCard
  wallet-name="Display Only"
  :amount="15000"
  card-number="1234 5678 9012 3456"
  background="bg-3"
  :clickable="false"
/>

<!-- Card without icon -->
<ColorCard
  wallet-name="Simple Card"
  amount="$5,000"
  card-number="Verification Required"
  background="bg-1"
  :show-icon="false"
/>
```

## Styling

The component uses the existing CSS classes and includes additional interactive styles:

```css
/* Clickable cards have hover effects */
.my-card-item.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Disabled cards are visually muted */
.my-card-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
```

You can customize styling by:
1. Using the `cardClass` prop for custom CSS classes
2. Using individual class props (`walletNameClass`, `amountClass`, etc.)
3. Overriding styles in your component's CSS

## Accessibility

The component includes accessibility features:
- ARIA labels for screen readers
- Keyboard navigation support (when clickable)
- Focus indicators
- Semantic HTML structure

## Migration from Static ColorCard

To migrate from the original static ColorCard:

1. Replace hardcoded content with dynamic props
2. Add event handlers for interactions
3. Use slots for custom content rendering
4. Update background class from static `bg-1` to dynamic `:background="bg-1"`

## Browser Support

- Vue 3.x
- Modern browsers (Chrome, Firefox, Safari, Edge)
- IE11+ (with polyfills)
