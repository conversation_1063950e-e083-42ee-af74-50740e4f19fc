# Dynamic Chart System

This project now includes a dynamic chart system that automatically discovers and manages all chart configurations from the plugins directory.

## Features

### 1. Automatic Chart Registry
- All chart configurations in `src/plugins/apexcharts/` are automatically registered
- Charts are organized by type (candlestick, column, line, donut, small)
- Easy access to any chart configuration by name

### 2. Dynamic ApexChart Component
The `ApexChart` component now supports both legacy and dynamic usage:

#### Legacy Usage (still supported)
```vue
<ApexChart
  chart-id="my-chart"
  :series="myData"
  :options="myOptions"
/>
```

#### Dynamic Usage (new)
```vue
<ApexChart
  chart-id="my-chart"
  chart-type="line-chart-twoline"
  :options="{ chart: { height: 400 } }"
/>
```

### 3. Chart Registry API

#### Available Functions
```javascript
import { 
  getChartConfig, 
  getAvailableChartTypes, 
  chartRegistry,
  initChart 
} from '@/plugins/charts.js'

// Get configuration for a specific chart type
const config = getChartConfig('candlestick-1')

// Get all available chart types
const types = getAvailableChartTypes()

// Initialize a chart dynamically
initChart('line-chart-1', '#my-container')
```

### 4. useCharts Composable
A Vue composable for managing charts in components:

```javascript
import { useCharts } from '@/composables/useCharts.js'

const { 
  availableChartTypes,
  getConfig,
  getConfigWithOverrides,
  getChartsByCategory,
  createCustomChart
} = useCharts()
```

## Available Chart Types

### Candlestick Charts
- `candlestick-1` through `candlestick-5`

### Column Charts  
- `column-chart-1` through `column-chart-9`

### Line Charts
- `line-chart-1`
- `line-chart-twoline`

### Donut Charts
- `donut-1`

### Small Charts
- `small-chart-1` through `small-chart-6`

## Usage Examples

### Basic Dynamic Chart
```vue
<template>
  <ApexChart
    chart-id="my-dynamic-chart"
    chart-type="candlestick-1"
    :options="{ chart: { height: 300 } }"
  />
</template>
```

### Chart with Custom Data
```vue
<template>
  <ApexChart
    chart-id="custom-chart"
    chart-type="line-chart-1"
    :series="customSeries"
    :options="customOptions"
  />
</template>

<script setup>
const customSeries = [
  { name: 'Sales', data: [10, 20, 30, 40] }
]

const customOptions = {
  chart: { height: 350 },
  colors: ['#ff0000']
}
</script>
```

### Chart Type Selector
```vue
<template>
  <div>
    <select v-model="selectedType">
      <option v-for="type in availableChartTypes" :value="type">
        {{ type }}
      </option>
    </select>
    
    <ApexChart
      chart-id="dynamic-selector"
      :chart-type="selectedType"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useCharts } from '@/composables/useCharts.js'

const { availableChartTypes } = useCharts()
const selectedType = ref('line-chart-1')
</script>
```

## Adding New Chart Types

1. Create a new chart configuration file in `src/plugins/apexcharts/`
2. Export the configuration as default:
   ```javascript
   export default {
     series: [...],
     chart: { type: 'line', height: 300 },
     // ... other options
   }
   ```
3. Add the import and registry entry in `src/plugins/charts.js`:
   ```javascript
   import newChart from './apexcharts/new-chart.js'
   
   export const chartRegistry = {
     // ... existing charts
     'new-chart': newChart,
   }
   ```

The chart will automatically be available for dynamic usage!

## Migration Guide

### From Legacy to Dynamic
Replace this:
```vue
<ApexChart
  chart-id="my-chart"
  :series="lineChartTwoline.series"
  :options="lineChartTwoline"
/>
```

With this:
```vue
<ApexChart
  chart-id="my-chart"
  chart-type="line-chart-twoline"
/>
```

### Benefits of Migration
- Cleaner component code
- Automatic chart discovery
- Better maintainability
- Type safety with chart names
- Easy chart switching
