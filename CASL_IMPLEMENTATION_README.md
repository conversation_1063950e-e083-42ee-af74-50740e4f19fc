# Vue 3 CASL Implementation Guide

This document provides a comprehensive guide to the CASL (isomorphic authorization library) implementation in this Vue 3 application.

## Overview

CASL is a powerful authorization library that allows you to define and check permissions in a declarative way. This implementation provides:

- Role-based access control (RBAC)
- Permission-based access control
- Reactive permission checking
- Route guards
- Component-level permission checks

## Project Structure

```
src/
├── abilities/
│   └── index.js              # CASL ability definitions
├── stores/
│   └── auth.js               # Authentication store with Pinia
├── plugins/
│   └── casl.js               # CASL Vue plugin configuration
├── composables/
│   └── useAuth.js            # Authentication composable
├── components/
│   └── auth/
│       ├── CanAccess.vue     # Permission-based rendering component
│       └── LoginForm.vue     # Login form component
├── views/
│   ├── LoginView.vue         # Login page
│   ├── AdminView.vue         # Admin-only page
│   └── UserView.vue          # User dashboard
└── router/
    └── index.js              # Router with permission guards
```

## Core Components

### 1. Ability Definitions (`src/abilities/index.js`)

Defines the permission structure and rules for different user roles:

```javascript
// Subjects (resources)
export const subjects = {
  USER: 'User',
  POST: 'Post',
  COMMENT: 'Comment',
  ADMIN_PANEL: 'AdminPanel',
  SETTINGS: 'Settings',
  DASHBOARD: 'Dashboard'
}

// Actions
export const actions = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  MANAGE: 'manage'
}
```

### 2. Authentication Store (`src/stores/auth.js`)

Manages user authentication state and integrates with CASL:

- User login/logout
- Permission checking methods
- Automatic ability updates
- Persistent state with localStorage

### 3. CASL Plugin (`src/plugins/casl.js`)

Integrates CASL with Vue 3:

- Provides global ability instance
- Makes permissions available in all components
- Enables reactive permission checking

### 4. Auth Composable (`src/composables/useAuth.js`)

Provides reusable authentication logic:

```javascript
const {
  user,
  isAuthenticated,
  userRole,
  can,
  cannot,
  login,
  logout
} = useAuth()
```

## User Roles and Permissions

### Admin
- **Full access** to everything (`can('manage', 'all')`)
- Can access admin panel
- Can manage all users, posts, and comments
- Can access all settings

### Moderator
- Can manage posts and comments
- Can read user information
- Can update non-admin users
- Can access admin panel (read-only)

### User
- Can create and manage their own posts
- Can create and manage their own comments
- Can read dashboard and settings
- Can update their own profile

### Guest (Not authenticated)
- No permissions
- Redirected to login for protected routes

## Usage Examples

### 1. Component-Level Permission Checks

```vue
<template>
  <!-- Show content only to admins -->
  <CanAccess action="read" subject="AdminPanel">
    <button class="btn btn-danger">Admin Action</button>
  </CanAccess>

  <!-- Show content only to specific roles -->
  <CanAccess role="moderator">
    <div class="moderator-content">Moderator only content</div>
  </CanAccess>

  <!-- Show content only to authenticated users -->
  <CanAccess require-auth>
    <div class="user-content">Authenticated user content</div>
  </CanAccess>

  <!-- Invert the check (show when user CANNOT access) -->
  <CanAccess action="read" subject="AdminPanel" not>
    <div class="alert">You don't have admin access</div>
  </CanAccess>
</template>
```

### 2. Programmatic Permission Checks

```javascript
import { useAuth } from '@/composables/useAuth'

const { can, cannot, isAdmin, canAccessAdminPanel } = useAuth()

// Check specific permissions
if (can('create', 'Post')) {
  // User can create posts
}

if (cannot('manage', 'User')) {
  // User cannot manage other users
}

// Check roles
if (isAdmin.value) {
  // User is an admin
}

// Use computed permissions
if (canAccessAdminPanel.value) {
  // User can access admin panel
}
```

### 3. Route Guards

Routes are automatically protected based on meta properties:

```javascript
{
  path: '/admin',
  component: AdminView,
  meta: {
    requiresAuth: true,
    requiresPermission: {
      action: 'read',
      subject: 'AdminPanel'
    }
  }
}
```

## Demo Accounts

The application includes demo accounts for testing:

| Role | Email | Password |
|------|-------|----------|
| Admin | <EMAIL> | admin123 |
| Moderator | <EMAIL> | mod123 |
| User | <EMAIL> | user123 |

## Navigation Integration

The navigation bar (`NavBar.vue`) is integrated with CASL:

- Shows/hides menu items based on permissions
- Displays user information when authenticated
- Provides login/logout functionality
- Admin panel link only visible to authorized users

## Key Features

### 1. Reactive Permissions
Permissions update automatically when user role changes or user logs in/out.

### 2. Route Protection
Routes are protected with guards that check authentication and permissions.

### 3. Conditional Rendering
The `CanAccess` component provides declarative permission-based rendering.

### 4. Role Switching Demo
The CASL example component allows switching between roles to see permission changes in real-time.

### 5. Persistent Authentication
User authentication state persists across browser sessions.

## Testing the Implementation

1. **Start the application**: `npm run dev`
2. **Visit the home page**: See the CASL demo section
3. **Try different roles**: Use the demo accounts to test different permission levels
4. **Check navigation**: Notice how menu items change based on user role
5. **Test route guards**: Try accessing `/admin` with different user roles
6. **Use the role switcher**: In the CASL demo section, switch between roles to see real-time permission changes

## Best Practices

1. **Define clear subjects and actions** in the abilities file
2. **Use the `CanAccess` component** for conditional rendering
3. **Leverage the `useAuth` composable** for programmatic checks
4. **Protect routes** with appropriate meta properties
5. **Keep permissions simple and readable**
6. **Test with different user roles** to ensure proper access control

## Extending the System

To add new permissions:

1. **Add new subjects/actions** in `src/abilities/index.js`
2. **Update role definitions** in the `defineAbilitiesFor` function
3. **Add new permission checks** in components using `CanAccess` or `useAuth`
4. **Update route guards** if needed for new protected routes

## Troubleshooting

### Permissions not updating
- Check that the ability instance is being updated in the auth store
- Ensure the CASL plugin is properly installed in `main.js`

### Route guards not working
- Verify the router guards are set up correctly
- Check that the auth store is initialized before routing

### Component permissions not reactive
- Make sure you're using the `useAuth` composable
- Check that the CASL Vue plugin is installed with reactive support

This implementation provides a solid foundation for authorization in Vue 3 applications and can be extended to meet specific requirements.
