{"name": "new-dashboard-bs", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@casl/ability": "6.7.3", "@casl/vue": "2.2.2", "@ditdot-dev/vue-flow-form": "^2.3.2", "@mdi/font": "^7.4.47", "@popperjs/core": "^2.11.8", "@unhead/vue": "^2.0.12", "apexcharts": "^5.3.1", "bootstrap": "^5.3.7", "bootstrap-select": "^1.13.18", "html2pdf.js": "^0.10.3", "jquery": "^3.7.1", "jquery-countto": "^1.2.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "pinia-plugin-persistedstate-2": "^2.0.30", "socket.io-client": "^4.8.1", "swiper": "^11.2.10", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue3-perfect-scrollbar": "2.0.0", "vue3-signature": "^0.2.4", "vue3-toastify": "^0.2.8", "vuetify": "^3.9.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/test-utils": "^2.4.6", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "3.5.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4"}}