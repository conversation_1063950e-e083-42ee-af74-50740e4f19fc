# Layout Implementation Documentation

## Overview

This document explains how the 4 layouts have been implemented to work with the existing theme settings system in `./src/plugins/theme-settings.js`.

## How It Works

The layout system works by applying CSS classes to the `.layout-wrap` element in the DOM. The theme settings use jQuery to manipulate these classes, and the CSS responds accordingly to change the layout appearance.

### Key Components

1. **App.vue** - Contains the main `.layout-wrap` element that theme settings target
2. **theme-settings.js** - Contains jQuery functions that apply CSS classes
3. **_theme-settings.scss** - Contains CSS rules that respond to the applied classes
4. **Layout Test Page** - Demonstrates how the layouts work

## The 4 Layouts

### 1. Default Layout
- **CSS Classes**: None (uses base `layout-wrap` styles)
- **Theme Setting**: Menu Style > Default (menu-click)
- **Description**: Standard full sidebar with complete navigation menu visible
- **Sidebar Width**: 256px

### 2. Boxed Layout  
- **CSS Classes**: `layout-width-boxed`
- **Theme Setting**: Layout Width > Boxed
- **Description**: Constrained width layout with centered content
- **Max Width**: 1440px
- **Behavior**: Centers the entire layout with constrained width

### 3. Icon Default Layout
- **CSS Classes**: `menu-style-icon-default`
- **Theme Setting**: Menu Style > Icon Default
- **Description**: Collapsed sidebar showing only icons
- **Sidebar Width**: 75px
- **Behavior**: Shows only menu icons, text hidden

### 4. Icon Hover Layout
- **CSS Classes**: `menu-style-icon`
- **Theme Setting**: Menu Style > Icon Hover  
- **Description**: Collapsed sidebar that expands on hover
- **Sidebar Width**: 75px (collapsed), 256px (on hover)
- **Behavior**: Expands to show full menu on hover

## Theme Settings Integration

The layouts integrate with the existing theme settings through these functions in `theme-settings.js`:

```javascript
// Menu Style toggles
export function menuStyle() {
  $('.menu-style .icon-hover').on('click', () => {
    $('.layout-wrap').addClass('menu-style-icon').removeClass('menu-style-icon-default')
  })
  
  $('.menu-style .icon-default').on('click', () => {
    $('.layout-wrap').addClass('menu-style-icon-default').removeClass('menu-style-icon')
  })
  
  $('.menu-style .menu-click').on('click', () => {
    $('.layout-wrap').removeClass('menu-style-icon menu-style-icon-default')
  })
}

// Layout Width toggles
export function layoutWidth() {
  $('.layout-width .boxed').on('click', () => {
    $('.layout-wrap').addClass('layout-width-boxed')
  })
  
  $('.layout-width .full').on('click', () => {
    $('.layout-wrap').removeClass('layout-width-boxed')
  })
}
```

## CSS Implementation

The layouts are implemented through CSS classes in `_theme-settings.scss`:

```scss
.layout-wrap {
  // Base styles
  
  &.layout-width-boxed {
    max-width: 1440px;
    margin: auto;
    // Boxed layout styles
  }
  
  &.menu-style-icon {
    .section-menu-left {
      width: 75px;
      // Icon hover styles
    }
  }
  
  &.menu-style-icon-default {
    .section-menu-left {
      width: 75px;
      // Icon default styles
    }
  }
}
```

## Testing the Layouts

### Via Theme Settings Panel
1. Click the gear icon in the header to open theme settings
2. Use "Menu Style" options to switch between menu layouts
3. Use "Layout Width" options to toggle boxed layout

### Via Layout Test Page
1. Navigate to `/layout-test`
2. Use the layout buttons to switch between layouts
3. Observe how the sidebar and content area change

## File Structure

```
src/
├── layouts/
│   ├── default.vue          # Default layout component (informational)
│   ├── boxed.vue           # Boxed layout component (informational)
│   ├── icon-default.vue   # Icon default layout component (informational)
│   └── icon-hover.vue     # Icon hover layout component (informational)
├── views/
│   └── LayoutTestView.vue  # Layout testing interface
├── plugins/
│   └── theme-settings.js   # Theme settings jQuery functions
└── assets/scss/
    └── _theme-settings.scss # Layout CSS styles
```

## Notes

- The layout Vue components in `src/layouts/` are primarily informational and demonstrate the different layout types
- The actual layout switching happens through CSS class manipulation on the main `.layout-wrap` element in App.vue
- This approach maintains compatibility with the existing theme settings system
- All layouts work with the existing sidebar collapse functionality
- The layouts are responsive and work with the existing mobile breakpoints
