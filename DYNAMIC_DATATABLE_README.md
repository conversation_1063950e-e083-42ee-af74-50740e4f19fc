# Dynamic DataTable Component

A powerful, flexible, and reusable Vue 3 data table component built from the original DataTable.vue. This component provides dynamic data binding, sorting, filtering, pagination, and customizable rendering.

## Features

- ✅ **Dynamic Data Binding** - Accept any array of objects as data
- ✅ **Configurable Columns** - Define columns with custom types and rendering
- ✅ **Search & Filter** - Built-in search functionality with custom filters
- ✅ **Sorting** - Click column headers to sort data
- ✅ **Row Selection** - Select individual rows or all rows with checkboxes
- ✅ **Pagination** - Built-in pagination with configurable page sizes
- ✅ **Custom Cell Rendering** - Use slots for custom column content
- ✅ **Multiple Column Types** - Support for text, image, status, date, currency columns
- ✅ **Export Functionality** - Built-in data export capabilities
- ✅ **Responsive Design** - Mobile-friendly responsive layout
- ✅ **Event System** - Comprehensive event emissions for interactions

## Basic Usage

```vue
<template>
  <DataTable
    :data="tableData"
    :columns="tableColumns"
    title="My Data Table"
    :searchable="true"
    :sortable="true"
    :selectable="true"
    :pagination="true"
    :items-per-page="10"
    @row-select="handleRowSelect"
    @row-click="handleRowClick"
  />
</template>

<script setup>
import DataTable from '@/components/shared/DataTable.vue'

const tableData = ref([
  { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active' },
  { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive' }
])

const tableColumns = ref([
  { key: 'name', label: 'Name', sortable: true },
  { key: 'email', label: 'Email', sortable: true },
  { key: 'status', label: 'Status', type: 'status', sortable: true }
])
</script>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `data` | Array | `[]` | Array of objects to display in the table |
| `columns` | Array | Required | Column configuration array |
| `title` | String | `'Data Table'` | Table title |
| `searchable` | Boolean | `true` | Enable/disable search functionality |
| `sortable` | Boolean | `true` | Enable/disable column sorting |
| `selectable` | Boolean | `true` | Enable/disable row selection |
| `pagination` | Boolean | `true` | Enable/disable pagination |
| `itemsPerPage` | Number | `10` | Number of items per page |
| `tableClass` | String | `''` | Additional CSS classes for the table |
| `showActions` | Boolean | `true` | Show/hide action buttons |
| `showFilters` | Boolean | `true` | Show/hide filter options |
| `showExport` | Boolean | `true` | Show/hide export button |

## Column Configuration

Each column object can have the following properties:

```javascript
{
  key: 'fieldName',           // Required: Object property to display
  label: 'Display Name',      // Required: Column header text
  type: 'text',              // Optional: Column type (text, image, status, date, currency)
  sortable: true,            // Optional: Enable sorting for this column
  cellClass: 'custom-class', // Optional: CSS class for cells
  
  // For image type columns:
  imageKey: 'imageUrl',      // Property containing image URL
  textKey: 'displayText',    // Property containing text to display
  imageClass: 'style-1',     // CSS class for image wrapper
  
  // For date type columns:
  format: 'short',           // Date format: 'short', 'long', 'time', 'default'
  
  // For currency type columns:
  currency: 'USD'            // Currency code for formatting
}
```

## Column Types

### Text Column (default)
```javascript
{ key: 'name', label: 'Name', type: 'text' }
```

### Image Column
```javascript
{
  key: 'user',
  label: 'User',
  type: 'image',
  imageKey: 'avatar',        // Property containing image URL
  textKey: 'name',           // Property containing display text
  imageClass: 'style-1'      // Optional CSS class
}
```

### Status Column
```javascript
{ key: 'status', label: 'Status', type: 'status' }
// Automatically applies styling based on status value
```

### Date Column
```javascript
{
  key: 'createdAt',
  label: 'Created',
  type: 'date',
  format: 'short'            // 'short', 'long', 'time', 'default'
}
```

### Currency Column
```javascript
{
  key: 'amount',
  label: 'Amount',
  type: 'currency',
  currency: 'USD'            // Currency code
}
```

## Custom Cell Rendering with Slots

Use named slots to customize column rendering:

```vue
<DataTable :data="data" :columns="columns">
  <!-- Custom status column -->
  <template #column-status="{ value, row, index }">
    <span class="badge" :class="getStatusClass(value)">
      {{ value }}
    </span>
  </template>
  
  <!-- Custom action column -->
  <template #column-actions="{ row }">
    <button @click="editRow(row)" class="btn btn-sm btn-primary">Edit</button>
    <button @click="deleteRow(row)" class="btn btn-sm btn-danger">Delete</button>
  </template>
</DataTable>
```

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `row-select` | `{ row, selected, selectedRows }` | Fired when row selection changes |
| `row-click` | `row` | Fired when a row is clicked |
| `sort-change` | `{ column, direction }` | Fired when sorting changes |
| `search` | `searchQuery` | Fired when search query changes |
| `filter-change` | `filterData` | Fired when filters change |
| `export-data` | `{ data, columns }` | Fired when export is requested |

## Advanced Examples

### Transaction Table
```vue
<DataTable
  :data="transactions"
  :columns="transactionColumns"
  @row-select="handleTransactionSelect"
  @export-data="exportTransactions"
>
  <template #column-amount="{ value }">
    <span class="currency-amount">{{ formatCurrency(value) }}</span>
  </template>
</DataTable>
```

### User Management Table
```vue
<DataTable
  :data="users"
  :columns="userColumns"
  :selectable="false"
  :items-per-page="20"
>
  <template #column-avatar="{ value, row }">
    <div class="user-avatar">
      <img :src="value" :alt="row.name" />
      <span>{{ row.name }}</span>
    </div>
  </template>
  
  <template #actions>
    <button class="btn btn-primary" @click="addUser">Add User</button>
  </template>
</DataTable>
```

## Styling

The component uses the existing CSS classes from the original DataTable and includes additional responsive styles. You can customize the appearance by:

1. Using the `tableClass` prop to add custom CSS classes
2. Overriding the default styles in your component
3. Using the `cellClass` property in column configuration

## Migration from Static DataTable

To migrate from the original static DataTable:

1. Replace static data with dynamic `:data` prop
2. Define column configuration with `:columns` prop
3. Remove hardcoded table rows from template
4. Add event handlers for interactions
5. Use slots for custom cell rendering

## Browser Support

- Vue 3.x
- Modern browsers (Chrome, Firefox, Safari, Edge)
- IE11+ (with polyfills)
