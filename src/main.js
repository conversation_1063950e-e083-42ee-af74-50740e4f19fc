// 1) Third‐party CSS
import 'bootstrap/dist/css/bootstrap.min.css'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

// 2) Your own CSS
import './assets/main.css'

// 3) Bootstrap JS
import 'bootstrap'
import 'bootstrap-select'
import 'bootstrap-select/dist/css/bootstrap-select.min.css'

// 4) jQuery + countTo
import $ from 'jquery'
window.$ = window.jQuery = $
import 'jquery-countto'
import ApexCharts from 'apexcharts'
window.ApexCharts = ApexCharts

// 5) Vue app bootstrap
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createHead } from '@unhead/vue/client'
import App from './App.vue'
import router from './router'
import Plugins from './plugins/vue-plugins'
import CASLPlugin from './plugins/casl'
import { useAuthStore } from './stores/auth'

const app = createApp(App)
const head = createHead()

const pinia = createPinia()
app.use(pinia)
app.use(head)
app.use(router)
app.use(Plugins)
app.use(CASLPlugin)

// Initialize auth store after pinia is set up
const authStore = useAuthStore()
authStore.initializeAuth()

app.mount('#app')
