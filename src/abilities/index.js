import { AbilityBuilder, createMongoAbility } from '@casl/ability'

// Define subjects (resources) that can be managed
export const subjects = {
  USER: 'User',
  POST: 'Post',
  COMMENT: 'Comment',
  ADMIN_PANEL: 'AdminPanel',
  SETTINGS: 'Settings',
  DASHBOARD: 'Dashboard'
}

// Define actions that can be performed
export const actions = {
  CREATE: 'create',
  READ: 'read',
  UPDATE: 'update',
  DELETE: 'delete',
  MANAGE: 'manage' // Special action that includes all actions
}

/**
 * Define abilities for different user roles
 * @param {Object} user - User object with role and other properties
 * @returns {Object} CASL Ability instance
 */
export function defineAbilitiesFor(user) {
  const { can, cannot, build } = new AbilityBuilder(createMongoAbility)

  if (!user) {
    // Guest user - no permissions
    return build()
  }

  // Common permissions for all authenticated users
  can(actions.READ, subjects.DASHBOARD)
  can(actions.READ, subjects.USER, { id: user.id }) // Users can read their own profile
  can(actions.UPDATE, subjects.USER, { id: user.id }) // Users can update their own profile

  // Role-based permissions
  switch (user.role) {
    case 'admin':
      // Ad<PERSON> can do everything
      can(actions.MANAGE, 'all')
      break

    case 'moderator':
      // Moderators can manage posts and comments
      can(actions.MANAGE, subjects.POST)
      can(actions.MANAGE, subjects.COMMENT)
      can(actions.READ, subjects.USER)
      can(actions.UPDATE, subjects.USER, { role: { $ne: 'admin' } }) // Cannot update admin users
      can(actions.READ, subjects.ADMIN_PANEL)
      break

    case 'user':
      // Regular users can create and manage their own content
      can(actions.CREATE, subjects.POST)
      can(actions.UPDATE, subjects.POST, { authorId: user.id })
      can(actions.DELETE, subjects.POST, { authorId: user.id })
      can(actions.CREATE, subjects.COMMENT)
      can(actions.UPDATE, subjects.COMMENT, { authorId: user.id })
      can(actions.DELETE, subjects.COMMENT, { authorId: user.id })
      can(actions.READ, subjects.SETTINGS)
      break

    default:
      // Unknown role - minimal permissions
      can(actions.READ, subjects.DASHBOARD)
      break
  }

  return build()
}

/**
 * Create ability instance for the current user
 * @param {Object} user - Current user object
 * @returns {Object} CASL Ability instance
 */
export function createAbility(user = null) {
  return defineAbilitiesFor(user)
}

// Export default ability instance (will be updated when user changes)
export const ability = createAbility()
