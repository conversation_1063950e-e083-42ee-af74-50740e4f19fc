import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import { ability, defineAbilitiesFor } from '@/abilities'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref(null)
  const isLoading = ref(false)
  const error = ref(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const userRole = computed(() => user.value?.role || null)
  const userName = computed(() => user.value?.name || '')
  const userEmail = computed(() => user.value?.email || '')

  // Mock users for demonstration
  const mockUsers = [
    {
      id: 1,
      email: '<EMAIL>',
      password: 'admin123',
      name: 'Admin User',
      role: 'admin',
      avatar: '/images/avatar/admin.png'
    },
    {
      id: 2,
      email: '<EMAIL>',
      password: 'mod123',
      name: 'Moderator User',
      role: 'moderator',
      avatar: '/images/avatar/moderator.png'
    },
    {
      id: 3,
      email: '<EMAIL>',
      password: 'user123',
      name: 'Regular User',
      role: 'user',
      avatar: '/images/avatar/user.png'
    }
  ]

  // Actions
  async function login(credentials) {
    isLoading.value = true
    error.value = null

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Find user by email and password (in real app, this would be an API call)
      const foundUser = mockUsers.find(
        u => u.email === credentials.email && u.password === credentials.password
      )

      if (!foundUser) {
        throw new Error('Invalid email or password')
      }

      // Remove password from user object
      const { password, ...userWithoutPassword } = foundUser
      user.value = userWithoutPassword

      // Update CASL abilities
      ability.update(defineAbilitiesFor(user.value))

      // Store user in localStorage for persistence
      localStorage.setItem('user', JSON.stringify(userWithoutPassword))

      return { success: true, user: userWithoutPassword }
    } catch (err) {
      error.value = err.message
      return { success: false, error: err.message }
    } finally {
      isLoading.value = false
    }
  }

  async function logout() {
    user.value = null
    error.value = null

    // Update CASL abilities (no user = no permissions)
    ability.update(defineAbilitiesFor(null))

    // Remove user from localStorage
    localStorage.removeItem('user')
  }

  function initializeAuth() {
    // Check if user is stored in localStorage
    const storedUser = localStorage.getItem('user')
    if (storedUser) {
      try {
        user.value = JSON.parse(storedUser)
        // Update CASL abilities
        ability.update(defineAbilitiesFor(user.value))
      } catch (err) {
        console.error('Error parsing stored user:', err)
        localStorage.removeItem('user')
      }
    }
  }

  function updateUser(userData) {
    if (user.value) {
      user.value = { ...user.value, ...userData }
      // Update CASL abilities
      ability.update(defineAbilitiesFor(user.value))
      // Update localStorage
      localStorage.setItem('user', JSON.stringify(user.value))
    }
  }

  function clearError() {
    error.value = null
  }

  // Check if user has specific permission
  function can(action, subject, field) {
    return ability.can(action, subject, field)
  }

  function cannot(action, subject, field) {
    return ability.cannot(action, subject, field)
  }

  return {
    // State
    user,
    isLoading,
    error,
    
    // Getters
    isAuthenticated,
    userRole,
    userName,
    userEmail,
    
    // Actions
    login,
    logout,
    initializeAuth,
    updateUser,
    clearError,
    can,
    cannot,
    
    // Mock data for demo
    mockUsers: computed(() => mockUsers.map(({ password, ...user }) => user))
  }
}, {
  persist: {
    key: 'auth-store',
    storage: localStorage,
    paths: ['user']
  }
})
