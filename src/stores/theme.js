import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // Theme state - matches the original Switcher.vue structure
  const activeSelections = ref({
    layoutWidth: 'full',
    menuStyle: 'default',
    menuPosition: 'fixed',
    headerPosition: 'fixed',
    loader: 'on',
    menuColor: '161326',
    headerColor: 'fff',
    primaryColor: '161326',
    backgroundColor: 'FFFFFF',
    themeMode: 'light'
  })

  // Vuetify theme instance (will be set when component mounts)
  let vuetifyTheme = null

  // Initialize Vuetify theme
  const initializeVuetifyTheme = (theme) => {
    vuetifyTheme = theme
  }

  // Color definitions matching the original implementation
  const colorDefinitions = {
    menu: {
      '161326': '#161326',
      '1E293B': '#1E293B',
      'fff': '#ffffff',
      '3A3043': '#3A3043'
    },
    header: {
      'fff': '#ffffff',
      '1E293B': '#1E293B',
      '161326': '#161326',
      '3A3043': '#3A3043'
    },
    primary: {
      '161326': '#161326',
      '2377FC': '#2377FC',
      '35988D': '#35988D',
      '7047D6': '#7047D6'
    },
    background: {
      'FFFFFF': '#FFFFFF',
      '252E3A': '#252E3A',
      '1E1D2A': '#1E1D2A',
      '1B2627': '#1B2627'
    }
  }

  // Computed properties
  const isDarkMode = computed(() => activeSelections.value.themeMode === 'dark')
  const isBoxedLayout = computed(() => activeSelections.value.layoutWidth === 'boxed')
  const isIconHoverMenu = computed(() => activeSelections.value.menuStyle === 'icon-hover')
  const isIconDefaultMenu = computed(() => activeSelections.value.menuStyle === 'icon-default')
  const isScrollableMenu = computed(() => activeSelections.value.menuPosition === 'scrollable')
  const isScrollableHeader = computed(() => activeSelections.value.headerPosition === 'scrollable')
  const isLoaderEnabled = computed(() => activeSelections.value.loader === 'on')

  // Layout width functions
  const applyLayoutWidth = (isBoxed) => {
    activeSelections.value.layoutWidth = isBoxed ? 'boxed' : 'full'

    // Apply CSS classes to layout-wrap element
    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      if (isBoxed) {
        layoutWrap.classList.add('layout-width-boxed')
        console.log('✅ Applied boxed layout - max-width: 1440px')
      } else {
        layoutWrap.classList.remove('layout-width-boxed')
        console.log('✅ Applied full width layout')
      }
    }
  }

  // Menu style functions
  const applyMenuStyle = (style) => {
    activeSelections.value.menuStyle = style

    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      // Remove all menu style classes first
      layoutWrap.classList.remove('menu-style-icon', 'menu-style-icon-default')

      // Apply the selected style
      if (style === 'icon-hover') {
        layoutWrap.classList.add('menu-style-icon')
        console.log('✅ Applied ICON HOVER menu style - 75px width, EXPANDS to 256px on hover')
      } else if (style === 'icon-default') {
        layoutWrap.classList.add('menu-style-icon-default')
        console.log('✅ Applied ICON DEFAULT menu style - 75px width, STAYS collapsed (no hover expansion)')
      } else {
        console.log('✅ Applied DEFAULT menu style - 256px width, full sidebar always visible')
      }
    }
  }

  // Position functions
  const applyMenuPosition = (isScrollable) => {
    activeSelections.value.menuPosition = isScrollable ? 'scrollable' : 'fixed'

    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      if (isScrollable) {
        layoutWrap.classList.add('menu-position-scrollable')
      } else {
        layoutWrap.classList.remove('menu-position-scrollable')
      }
    }
  }

  const applyHeaderPosition = (isScrollable) => {
    activeSelections.value.headerPosition = isScrollable ? 'scrollable' : 'fixed'

    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      if (isScrollable) {
        layoutWrap.classList.add('header-position-scrollable')
      } else {
        layoutWrap.classList.remove('header-position-scrollable')
      }
    }
  }

  // Loader function
  const applyLoader = (isEnabled) => {
    activeSelections.value.loader = isEnabled ? 'on' : 'off'

    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      if (isEnabled) {
        layoutWrap.classList.remove('loader-off')
      } else {
        layoutWrap.classList.add('loader-off')
      }
    }
  }

  // Color theme functions
  const applyMenuColor = (color) => {
    activeSelections.value.menuColor = color

    const layoutWrap = document.querySelector('.layout-wrap')
    const logoHeader = document.querySelector('#logo_header')

    if (layoutWrap) {
      layoutWrap.setAttribute('data-menu-background', `colors-menu-${color}`)
      console.log(`✅ Applied menu color: ${color}`)

      // Update logo based on color
      if (logoHeader) {
        const lightLogo = logoHeader.getAttribute('data-light')
        const darkLogo = logoHeader.getAttribute('data-dark')

        if (color === 'fff') {
          logoHeader.src = darkLogo || logoHeader.src
        } else {
          logoHeader.src = lightLogo || logoHeader.src
        }
      }
    }

    // Update Vuetify theme if available
    if (vuetifyTheme) {
      const colorValue = colorDefinitions.menu[color]
      if (colorValue) {
        vuetifyTheme.current.value.colors['menu-background'] = colorValue
      }
    }
  }

  const applyHeaderColor = (color) => {
    activeSelections.value.headerColor = color

    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      layoutWrap.setAttribute('data-colors-header', `colors-header-${color}`)
      console.log(`Applied header color: ${color}`)
    }

    // Update Vuetify theme if available
    if (vuetifyTheme) {
      const colorValue = colorDefinitions.header[color]
      if (colorValue) {
        vuetifyTheme.current.value.colors['header-background'] = colorValue
      }
    }
  }

  const applyPrimaryColor = (color) => {
    activeSelections.value.primaryColor = color

    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      layoutWrap.setAttribute('data-theme-primary', `theme-primary-${color}`)
      console.log(`Applied primary color: ${color}`)
    }

    // Update Vuetify theme if available
    if (vuetifyTheme) {
      const colorValue = colorDefinitions.primary[color]
      if (colorValue) {
        vuetifyTheme.current.value.colors.primary = colorValue
      }
    }
  }

  const applyBackgroundColor = (color) => {
    activeSelections.value.backgroundColor = color

    const body = document.body
    if (body) {
      body.setAttribute('data-theme-background', `theme-background-${color}`)
      console.log(`Applied background color: ${color}`)
    }

    // Update Vuetify theme if available
    if (vuetifyTheme) {
      const colorValue = colorDefinitions.background[color]
      if (colorValue) {
        vuetifyTheme.current.value.colors.background = colorValue
      }
    }
  }

  // Dark/Light theme functions
  const applyDarkTheme = () => {
    activeSelections.value.themeMode = 'dark'
    document.body.classList.add('dark-theme')
    localStorage.setItem('toggled', 'dark-theme')

    if (vuetifyTheme) {
      vuetifyTheme.global.name.value = 'dark'
    }

    console.log('✅ Applied dark theme')
  }

  const applyLightTheme = () => {
    activeSelections.value.themeMode = 'light'
    document.body.classList.remove('dark-theme')
    localStorage.setItem('toggled', 'light-theme')

    if (vuetifyTheme) {
      vuetifyTheme.global.name.value = 'light'
    }

    console.log('✅ Applied light theme')
  }

  // Clear functions
  const clearAllStyles = () => {
    const layoutWrap = document.querySelector('.layout-wrap')
    const body = document.body

    if (layoutWrap) {
      // Remove layout classes
      layoutWrap.classList.remove(
        'layout-width-boxed',
        'menu-style-icon',
        'menu-style-icon-default',
        'menu-position-scrollable',
        'header-position-scrollable',
        'loader-off'
      )

      // Remove color attributes
      layoutWrap.removeAttribute('data-menu-background')
      layoutWrap.removeAttribute('data-colors-header')
      layoutWrap.removeAttribute('data-theme-primary')
    }

    if (body) {
      body.removeAttribute('data-theme-background')
      body.classList.remove('dark-theme')
    }

    // Reset localStorage and active selections
    localStorage.setItem('toggled', 'light-theme')

    // Reset active selections to defaults
    activeSelections.value = {
      layoutWidth: 'full',
      menuStyle: 'default',
      menuPosition: 'fixed',
      headerPosition: 'fixed',
      loader: 'on',
      menuColor: '161326',
      headerColor: 'fff',
      primaryColor: '161326',
      backgroundColor: 'FFFFFF',
      themeMode: 'light'
    }

    // Reset Vuetify theme
    if (vuetifyTheme) {
      vuetifyTheme.global.name.value = 'light'
    }

    console.log('✅ All styles cleared and reset to defaults')
  }

  const clearAllColors = () => {
    const layoutWrap = document.querySelector('.layout-wrap')
    const body = document.body

    if (layoutWrap) {
      layoutWrap.removeAttribute('data-menu-background')
      layoutWrap.removeAttribute('data-colors-header')
      layoutWrap.removeAttribute('data-theme-primary')
    }

    if (body) {
      body.removeAttribute('data-theme-background')
    }

    console.log('All colors cleared')
  }

  // Initialize theme from localStorage
  const initializeTheme = () => {
    const savedTheme = localStorage.getItem('toggled')
    if (savedTheme === 'dark-theme') {
      applyDarkTheme()
    } else {
      applyLightTheme()
    }
  }

  return {
    // State
    activeSelections,
    colorDefinitions,

    // Computed
    isDarkMode,
    isBoxedLayout,
    isIconHoverMenu,
    isIconDefaultMenu,
    isScrollableMenu,
    isScrollableHeader,
    isLoaderEnabled,

    // Actions
    initializeVuetifyTheme,
    applyLayoutWidth,
    applyMenuStyle,
    applyMenuPosition,
    applyHeaderPosition,
    applyLoader,
    applyMenuColor,
    applyHeaderColor,
    applyPrimaryColor,
    applyBackgroundColor,
    applyDarkTheme,
    applyLightTheme,
    clearAllStyles,
    clearAllColors,
    initializeTheme
  }
}, {
  persist: {
    key: 'theme-settings',
    storage: localStorage,
    paths: ['activeSelections']
  }
})
