import { ref, computed } from 'vue'
import { getChartConfig, getAvailableChartTypes, chartRegistry } from '@/plugins/charts.js'

/**
 * Composable for managing dynamic charts
 */
export function useCharts() {
  const selectedChartType = ref(null)
  
  // Get all available chart types
  const availableChartTypes = computed(() => getAvailableChartTypes())
  
  // Get chart configuration for a specific type
  const getConfig = (chartType) => {
    return getChartConfig(chartType)
  }
  
  // Get chart configuration with custom overrides
  const getConfigWithOverrides = (chartType, overrides = {}) => {
    const baseConfig = getChartConfig(chartType)
    if (!baseConfig) return null
    
    return {
      ...baseConfig,
      ...overrides,
      // Merge chart options specifically
      chart: {
        ...baseConfig.chart,
        ...overrides.chart,
      },
    }
  }
  
  // Get charts by category
  const getChartsByCategory = () => {
    const categories = {
      candlestick: [],
      column: [],
      line: [],
      donut: [],
      small: [],
    }
    
    Object.keys(chartRegistry).forEach(chartType => {
      if (chartType.startsWith('candlestick')) {
        categories.candlestick.push(chartType)
      } else if (chartType.startsWith('column-chart')) {
        categories.column.push(chartType)
      } else if (chartType.startsWith('line-chart')) {
        categories.line.push(chartType)
      } else if (chartType.startsWith('donut')) {
        categories.donut.push(chartType)
      } else if (chartType.startsWith('small-chart')) {
        categories.small.push(chartType)
      }
    })
    
    return categories
  }
  
  // Create a chart configuration with custom data
  const createCustomChart = (baseChartType, customData = {}) => {
    const baseConfig = getChartConfig(baseChartType)
    if (!baseConfig) return null
    
    return {
      ...baseConfig,
      ...customData,
      series: customData.series || baseConfig.series,
    }
  }
  
  return {
    selectedChartType,
    availableChartTypes,
    getConfig,
    getConfigWithOverrides,
    getChartsByCategory,
    createCustomChart,
  }
}
