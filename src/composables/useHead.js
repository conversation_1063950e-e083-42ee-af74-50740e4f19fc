import { useHead as useUnhead } from '@unhead/vue'
import { computed, watch } from 'vue'
import { useRoute } from 'vue-router'

/**
 * Head management composable
 * Provides reusable head tag management functionality using @unhead/vue
 */
export function useHead(headConfig = {}) {
  const route = useRoute()

  // Default head configuration
  const defaultConfig = {
    title: 'Dashboard',
    titleTemplate: '%s | Your App Name',
    meta: [
      {
        name: 'description',
        content: 'Modern dashboard application built with Vue.js',
      },
      {
        name: 'viewport',
        content: 'width=device-width, initial-scale=1.0',
      },
      {
        property: 'og:type',
        content: 'website',
      },
    ],
    link: [
      {
        rel: 'icon',
        type: 'image/x-icon',
        href: '/favicon.ico',
      },
    ],
  }

  // Merge default config with provided config
  const mergedConfig = computed(() => {
    if (typeof headConfig === 'function') {
      return { ...defaultConfig, ...headConfig() }
    }
    return { ...defaultConfig, ...headConfig }
  })

  // Use @unhead/vue to manage head tags
  const head = useUnhead(mergedConfig)

  // Helper function to update title dynamically
  const setTitle = (title) => {
    head.patch({
      title,
    })
  }

  // Helper function to update meta description
  const setDescription = (description) => {
    head.patch({
      meta: [
        {
          name: 'description',
          content: description,
        },
      ],
    })
  }

  // Helper function to update Open Graph meta tags
  const setOpenGraph = (ogData) => {
    const ogMeta = Object.entries(ogData).map(([key, value]) => ({
      property: `og:${key}`,
      content: value,
    }))

    head.patch({
      meta: ogMeta,
    })
  }

  // Helper function to add custom meta tags
  const addMeta = (metaTags) => {
    head.patch({
      meta: Array.isArray(metaTags) ? metaTags : [metaTags],
    })
  }

  // Helper function to add custom link tags
  const addLink = (linkTags) => {
    head.patch({
      link: Array.isArray(linkTags) ? linkTags : [linkTags],
    })
  }

  // Helper function to add custom script tags
  const addScript = (scriptTags) => {
    head.patch({
      script: Array.isArray(scriptTags) ? scriptTags : [scriptTags],
    })
  }

  // Auto-update title based on route meta
  watch(
    () => route.meta,
    (meta) => {
      if (meta?.title) {
        setTitle(meta.title)
      }
    },
    { immediate: true },
  )

  return {
    head,
    setTitle,
    setDescription,
    setOpenGraph,
    addMeta,
    addLink,
    addScript,
  }
}

/**
 * Page-specific head management composable
 * Automatically sets head tags based on route configuration
 */
export function usePageHead() {
  const route = useRoute()

  const pageConfig = computed(() => {
    const meta = route.meta || {}
    const routeName = route.name || 'Page'

    return {
      title: meta.title || routeName.charAt(0).toUpperCase() + routeName.slice(1),
      meta: [
        {
          name: 'description',
          content: meta.description || `${routeName} page`,
        },
        ...(meta.keywords
          ? [
              {
                name: 'keywords',
                content: meta.keywords,
              },
            ]
          : []),
        ...(meta.author
          ? [
              {
                name: 'author',
                content: meta.author,
              },
            ]
          : []),
      ],
    }
  })

  return useHead(pageConfig)
}
