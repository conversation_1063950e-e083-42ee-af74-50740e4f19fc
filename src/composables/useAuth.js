import { computed, inject } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useAbility } from '@casl/vue'
import { subjects, actions } from '@/abilities'

/**
 * Authentication composable
 * Provides reusable authentication logic and permission checking
 */
export function useAuth() {
  const authStore = useAuthStore()
  const ability = useAbility()

  // Auth state
  const user = computed(() => authStore.user)
  const isAuthenticated = computed(() => authStore.isAuthenticated)
  const isLoading = computed(() => authStore.isLoading)
  const error = computed(() => authStore.error)
  const userRole = computed(() => authStore.userRole)

  // Permission checking helpers
  const can = (action, subject, field) => {
    return ability.can(action, subject, field)
  }

  const cannot = (action, subject, field) => {
    return ability.cannot(action, subject, field)
  }

  // Role checking helpers
  const isAdmin = computed(() => userRole.value === 'admin')
  const isModerator = computed(() => userRole.value === 'moderator')
  const isUser = computed(() => userRole.value === 'user')

  // Common permission checks
  const canAccessAdminPanel = computed(() => can(actions.READ, subjects.ADMIN_PANEL))
  const canManageUsers = computed(() => can(actions.MANAGE, subjects.USER))
  const canCreatePosts = computed(() => can(actions.CREATE, subjects.POST))
  const canManagePosts = computed(() => can(actions.MANAGE, subjects.POST))

  // Auth actions
  const login = async (credentials) => {
    return await authStore.login(credentials)
  }

  const logout = async () => {
    await authStore.logout()
  }

  const clearError = () => {
    authStore.clearError()
  }

  // Check if user owns a resource
  const isOwner = (resource) => {
    if (!user.value || !resource) return false
    return resource.authorId === user.value.id || resource.userId === user.value.id
  }

  // Check if user can perform action on specific resource
  const canPerformAction = (action, subject, resource = null) => {
    if (resource) {
      return can(action, subject, resource)
    }
    return can(action, subject)
  }

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,
    userRole,

    // Role checks
    isAdmin,
    isModerator,
    isUser,

    // Permission checks
    can,
    cannot,
    canAccessAdminPanel,
    canManageUsers,
    canCreatePosts,
    canManagePosts,

    // Utilities
    isOwner,
    canPerformAction,

    // Actions
    login,
    logout,
    clearError,

    // Constants for convenience
    subjects,
    actions
  }
}
