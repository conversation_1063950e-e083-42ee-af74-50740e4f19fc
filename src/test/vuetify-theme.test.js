import { describe, it, expect, beforeEach } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useThemeStore } from '@/stores/theme'

describe('Vuetify Theme Store', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia())
  })

  it('should initialize with default values', () => {
    const themeStore = useThemeStore()
    
    expect(themeStore.activeSelections).toEqual({
      layoutWidth: 'full',
      menuStyle: 'default',
      menuPosition: 'fixed',
      headerPosition: 'fixed',
      loader: 'on',
      menuColor: '161326',
      headerColor: 'fff',
      primaryColor: '161326',
      backgroundColor: 'FFFFFF',
      themeMode: 'light'
    })
  })

  it('should have correct computed properties', () => {
    const themeStore = useThemeStore()
    
    expect(themeStore.isDarkMode).toBe(false)
    expect(themeStore.isBoxedLayout).toBe(false)
    expect(themeStore.isIconHoverMenu).toBe(false)
    expect(themeStore.isIconDefaultMenu).toBe(false)
    expect(themeStore.isScrollableMenu).toBe(false)
    expect(themeStore.isScrollableHeader).toBe(false)
    expect(themeStore.isLoaderEnabled).toBe(true)
  })

  it('should update layout width correctly', () => {
    const themeStore = useThemeStore()
    
    // Test boxed layout
    themeStore.applyLayoutWidth(true)
    expect(themeStore.activeSelections.layoutWidth).toBe('boxed')
    expect(themeStore.isBoxedLayout).toBe(true)
    
    // Test full layout
    themeStore.applyLayoutWidth(false)
    expect(themeStore.activeSelections.layoutWidth).toBe('full')
    expect(themeStore.isBoxedLayout).toBe(false)
  })

  it('should update menu style correctly', () => {
    const themeStore = useThemeStore()
    
    // Test icon-hover style
    themeStore.applyMenuStyle('icon-hover')
    expect(themeStore.activeSelections.menuStyle).toBe('icon-hover')
    expect(themeStore.isIconHoverMenu).toBe(true)
    
    // Test icon-default style
    themeStore.applyMenuStyle('icon-default')
    expect(themeStore.activeSelections.menuStyle).toBe('icon-default')
    expect(themeStore.isIconDefaultMenu).toBe(true)
    
    // Test default style
    themeStore.applyMenuStyle('default')
    expect(themeStore.activeSelections.menuStyle).toBe('default')
    expect(themeStore.isIconHoverMenu).toBe(false)
    expect(themeStore.isIconDefaultMenu).toBe(false)
  })

  it('should update theme mode correctly', () => {
    const themeStore = useThemeStore()
    
    // Test dark theme
    themeStore.applyDarkTheme()
    expect(themeStore.activeSelections.themeMode).toBe('dark')
    expect(themeStore.isDarkMode).toBe(true)
    
    // Test light theme
    themeStore.applyLightTheme()
    expect(themeStore.activeSelections.themeMode).toBe('light')
    expect(themeStore.isDarkMode).toBe(false)
  })

  it('should update color settings correctly', () => {
    const themeStore = useThemeStore()
    
    // Test menu color
    themeStore.applyMenuColor('fff')
    expect(themeStore.activeSelections.menuColor).toBe('fff')
    
    // Test header color
    themeStore.applyHeaderColor('161326')
    expect(themeStore.activeSelections.headerColor).toBe('161326')
    
    // Test primary color
    themeStore.applyPrimaryColor('2377FC')
    expect(themeStore.activeSelections.primaryColor).toBe('2377FC')
    
    // Test background color
    themeStore.applyBackgroundColor('252E3A')
    expect(themeStore.activeSelections.backgroundColor).toBe('252E3A')
  })

  it('should clear all styles correctly', () => {
    const themeStore = useThemeStore()
    
    // Change some settings
    themeStore.applyDarkTheme()
    themeStore.applyLayoutWidth(true)
    themeStore.applyMenuStyle('icon-hover')
    themeStore.applyMenuColor('fff')
    
    // Clear all styles
    themeStore.clearAllStyles()
    
    // Verify reset to defaults
    expect(themeStore.activeSelections).toEqual({
      layoutWidth: 'full',
      menuStyle: 'default',
      menuPosition: 'fixed',
      headerPosition: 'fixed',
      loader: 'on',
      menuColor: '161326',
      headerColor: 'fff',
      primaryColor: '161326',
      backgroundColor: 'FFFFFF',
      themeMode: 'light'
    })
  })

  it('should have correct color definitions', () => {
    const themeStore = useThemeStore()
    
    expect(themeStore.colorDefinitions.menu).toEqual({
      '161326': '#161326',
      '1E293B': '#1E293B',
      'fff': '#ffffff',
      '3A3043': '#3A3043'
    })
    
    expect(themeStore.colorDefinitions.primary).toEqual({
      '161326': '#161326',
      '2377FC': '#2377FC',
      '35988D': '#35988D',
      '7047D6': '#7047D6'
    })
  })

  it('should update position settings correctly', () => {
    const themeStore = useThemeStore()
    
    // Test menu position
    themeStore.applyMenuPosition(true)
    expect(themeStore.activeSelections.menuPosition).toBe('scrollable')
    expect(themeStore.isScrollableMenu).toBe(true)
    
    themeStore.applyMenuPosition(false)
    expect(themeStore.activeSelections.menuPosition).toBe('fixed')
    expect(themeStore.isScrollableMenu).toBe(false)
    
    // Test header position
    themeStore.applyHeaderPosition(true)
    expect(themeStore.activeSelections.headerPosition).toBe('scrollable')
    expect(themeStore.isScrollableHeader).toBe(true)
    
    themeStore.applyHeaderPosition(false)
    expect(themeStore.activeSelections.headerPosition).toBe('fixed')
    expect(themeStore.isScrollableHeader).toBe(false)
  })

  it('should update loader setting correctly', () => {
    const themeStore = useThemeStore()
    
    // Test disable loader
    themeStore.applyLoader(false)
    expect(themeStore.activeSelections.loader).toBe('off')
    expect(themeStore.isLoaderEnabled).toBe(false)
    
    // Test enable loader
    themeStore.applyLoader(true)
    expect(themeStore.activeSelections.loader).toBe('on')
    expect(themeStore.isLoaderEnabled).toBe(true)
  })
})
