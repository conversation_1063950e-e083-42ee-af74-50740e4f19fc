// Simple test to verify useHead functionality
// This can be run in the browser console or as a unit test

export function testHeadFunctionality() {
  console.log('🧪 Testing useHead functionality...')
  
  // Test 1: Check if document title is being set
  const originalTitle = document.title
  console.log('📋 Original title:', originalTitle)
  
  // Test 2: Check if meta tags are present
  const metaDescription = document.querySelector('meta[name="description"]')
  const metaViewport = document.querySelector('meta[name="viewport"]')
  
  console.log('📝 Meta description:', metaDescription?.content || 'Not found')
  console.log('📱 Meta viewport:', metaViewport?.content || 'Not found')
  
  // Test 3: Check if head tags are reactive
  const headTags = document.head.children
  console.log('🏷️ Total head tags:', headTags.length)
  
  // Test 4: Look for unhead-specific attributes
  const unheadTags = document.querySelectorAll('[data-unhead]')
  console.log('🎯 Unhead managed tags:', unheadTags.length)
  
  // Test results
  const results = {
    titleSet: document.title !== 'Vite App', // Default Vite title
    metaDescriptionExists: !!metaDescription,
    metaViewportExists: !!metaViewport,
    unheadTagsFound: unheadTags.length > 0,
    totalHeadTags: headTags.length
  }
  
  console.log('✅ Test Results:', results)
  
  // Overall test status
  const allTestsPassed = Object.values(results).every(result => 
    typeof result === 'boolean' ? result : result > 0
  )
  
  console.log(allTestsPassed ? '🎉 All tests passed!' : '❌ Some tests failed')
  
  return results
}

// Auto-run test if in browser environment
if (typeof window !== 'undefined') {
  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testHeadFunctionality)
  } else {
    // DOM is already ready
    setTimeout(testHeadFunctionality, 1000) // Wait a bit for Vue to initialize
  }
}
