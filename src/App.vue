<script setup>
import { RouterView } from 'vue-router'
import { ref } from 'vue'
import { useAuth } from '@/composables/useAuth'
import NavBar from '@/components/shared/NavBar.vue'
import Switcher from '@/components/shared/Switcher.vue'

const collapsed = ref(false)
const { isAuthenticated } = useAuth()
</script>

<template>
  <div id="wrapper">
    <div id="page">
      <Switcher />
      <!-- Show NavBar layout when authenticated -->
      <div v-if="isAuthenticated" :class="['layout-wrap', { 'full-width': collapsed }]">
        <NavBar v-model:collapsed="collapsed">
          <!-- this will land inside the slot -->
          <div class="main-content">
            <RouterView />
          </div>
        </NavBar>
      </div>

      <!-- Show full-width layout when not authenticated -->
      <div v-else class="layout-wrap full-width no-navbar">
        <div class="main-content">
          <RouterView />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
header {
  line-height: 1.5;
  max-height: 100vh;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

nav {
  width: 100%;
  font-size: 12px;
  text-align: center;
  margin-top: 2rem;
}

nav a.router-link-exact-active {
  color: var(--color-text);
}

/* Styles for when NavBar is not shown */
.no-navbar {
  background: #f8f9fa;
  min-height: 100vh;
}

.no-navbar .main-content {
  padding: 0;
  margin: 0;
  width: 100%;
  min-height: 100vh;
}

nav a.router-link-exact-active:hover {
  background-color: transparent;
}

nav a {
  display: inline-block;
  padding: 0 1rem;
  border-left: 1px solid var(--color-border);
}

nav a:first-of-type {
  border: 0;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }

  nav {
    text-align: left;
    margin-left: -1rem;
    font-size: 1rem;

    padding: 1rem 0;
    margin-top: 1rem;
  }
}
</style>
