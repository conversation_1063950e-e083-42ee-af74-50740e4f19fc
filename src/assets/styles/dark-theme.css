/* Dark Theme Styles */
body.dark-theme {
  background-color: #1a1a1a !important;
  color: #e5e5e5 !important;
}

body.dark-theme #app {
  background-color: #1a1a1a !important;
}

body.dark-theme #wrapper {
  background-color: #1a1a1a !important;
}

body.dark-theme #page {
  background-color: #1a1a1a !important;
}

body.dark-theme .layout-wrap {
  background-color: #1a1a1a !important;
}

/* Main content areas */
body.dark-theme .main-content {
  background-color: #1a1a1a !important;
  color: #e5e5e5 !important;
}

body.dark-theme .section-content-right {
  background-color: #1a1a1a !important;
}

body.dark-theme .main-content-inner {
  background-color: #1a1a1a !important;
}

body.dark-theme .main-content-wrap {
  background-color: #1a1a1a !important;
}

/* Header */
body.dark-theme .header-dashboard {
  background-color: #2d2d2d !important;
  border-bottom: 1px solid #404040 !important;
}

body.dark-theme .header-dashboard h6 {
  color: #e5e5e5 !important;
}

body.dark-theme .header-dashboard .form-search input {
  background-color: #404040 !important;
  border-color: #555 !important;
  color: #e5e5e5 !important;
}

body.dark-theme .header-dashboard .form-search input::placeholder {
  color: #999 !important;
}

body.dark-theme .header-dashboard .button-submit i {
  color: #e5e5e5 !important;
}

/* Sidebar */
body.dark-theme .section-menu-left {
  background-color: #2d2d2d !important;
}

body.dark-theme .section-menu-left::before {
  background-color: #2d2d2d !important;
}

body.dark-theme .box-logo {
  background-color: #2d2d2d !important;
}

.dark-theme .menu-heading {
  color: #999;
}

.dark-theme .menu-item .menu-item-button {
  color: #e5e5e5;
}

.dark-theme .menu-item .menu-item-button:hover {
  background-color: #404040;
}

.dark-theme .menu-item .menu-item-button .text {
  color: #e5e5e5;
}

.dark-theme .menu-item .menu-item-button .icon i {
  color: #999;
}

.dark-theme .menu-item .menu-item-button:hover .icon i {
  color: #e5e5e5;
}

/* Cards and widgets */
body.dark-theme .wg-box,
body.dark-theme .wg-card {
  background-color: #2d2d2d !important;
  border-color: #404040 !important;
}

body.dark-theme .wg-box .title,
body.dark-theme .wg-card .title {
  color: #e5e5e5 !important;
}

body.dark-theme .wg-box,
body.dark-theme .wg-card,
body.dark-theme .widget-tabs,
body.dark-theme .tf-container {
  background-color: #2d2d2d !important;
}

/* Override any white backgrounds */
body.dark-theme .bg-white,
body.dark-theme .bg-Gainsboro,
body.dark-theme .style-1,
body.dark-theme .shadow-none {
  background-color: #2d2d2d !important;
}

/* Force dark theme on all major containers */
body.dark-theme .tf-container,
body.dark-theme .main-content,
body.dark-theme .main-content-inner,
body.dark-theme .main-content-wrap,
body.dark-theme .section-content-right,
body.dark-theme .layout-wrap,
body.dark-theme #page,
body.dark-theme #wrapper,
body.dark-theme #app {
  background-color: #1a1a1a !important;
  color: #e5e5e5 !important;
}

/* Force dark theme on all widgets and cards */
body.dark-theme .wg-box,
body.dark-theme .wg-card,
body.dark-theme .widget-tabs,
body.dark-theme .widget-content-tab,
body.dark-theme .widget-content-inner {
  background-color: #2d2d2d !important;
  color: #e5e5e5 !important;
  border-color: #404040 !important;
}

/* Dropdowns */
.dark-theme .dropdown-menu {
  background-color: #2d2d2d;
  border-color: #404040;
}

.dark-theme .dropdown-menu .dropdown-item {
  color: #e5e5e5;
}

.dark-theme .dropdown-menu .dropdown-item:hover {
  background-color: #404040;
}

/* User menu */
.dark-theme .header-user .name {
  color: #e5e5e5;
}

.dark-theme .header-user .text-Gray {
  color: #999;
}

/* Header items */
.dark-theme .header-item {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-theme .header-item i {
  color: #e5e5e5;
}

/* Tables */
.dark-theme table {
  color: #e5e5e5;
}

.dark-theme table th {
  color: #999;
  border-bottom-color: #404040;
}

.dark-theme table td {
  border-bottom-color: #404040;
}

/* Buttons */
.dark-theme .tf-button {
  background-color: #404040;
  border-color: #555;
  color: #e5e5e5;
}

.dark-theme .tf-button:hover {
  background-color: #555;
}

/* Forms */
.dark-theme input,
.dark-theme select,
.dark-theme textarea {
  background-color: #404040;
  border-color: #555;
  color: #e5e5e5;
}

.dark-theme input::placeholder {
  color: #999;
}

/* Scrollbars */
.dark-theme ::-webkit-scrollbar {
  width: 8px;
}

.dark-theme ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.dark-theme ::-webkit-scrollbar-thumb {
  background: #555;
  border-radius: 4px;
}

.dark-theme ::-webkit-scrollbar-thumb:hover {
  background: #666;
}

/* Text colors */
.dark-theme .text-Black {
  color: #e5e5e5 !important;
}

.dark-theme .text-Gray {
  color: #999 !important;
}

.dark-theme .text-White {
  color: #e5e5e5 !important;
}

/* Borders */
.dark-theme .border,
.dark-theme [class*="border"] {
  border-color: #404040 !important;
}

/* Shadows */
.dark-theme .shadow,
.dark-theme [class*="shadow"] {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2) !important;
}

/* Charts and graphics */
.dark-theme .chart-small,
.dark-theme .candlestick-chart {
  background-color: #2d2d2d;
}

/* Specific component overrides */
.dark-theme .offcanvas {
  background-color: #2d2d2d;
  color: #e5e5e5;
}

.dark-theme .offcanvas-header {
  border-bottom-color: #404040;
}

.dark-theme .btn-close {
  filter: invert(1);
}

/* Theme switcher specific */
.dark-theme .color-option {
  border-color: #555;
}

.dark-theme .color-option:hover {
  border-color: #60a5fa;
}

.dark-theme .form-check-input {
  background-color: #404040;
  border-color: #555;
}

.dark-theme .form-check-input:checked {
  background-color: #60a5fa;
  border-color: #60a5fa;
}

.dark-theme .btn-outline-secondary {
  border-color: #555;
  color: #e5e5e5;
}

.dark-theme .btn-outline-secondary:hover {
  background-color: #555;
  border-color: #666;
}

/* Injected theme switcher button styles */
.divider {
  width: 1px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 12px;
}

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.setting:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.setting i {
  font-size: 18px;
  color: #6b7280;
}

.setting:hover i {
  color: #374151;
}

/* Dark theme styles for injected elements */
body.dark-theme .divider {
  background-color: #404040;
}

body.dark-theme .setting {
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .setting:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

body.dark-theme .setting i {
  color: #e5e5e5;
}

body.dark-theme .setting:hover i {
  color: #ffffff;
}

/* Enhanced theme switcher offcanvas styling */
body.dark-theme .offcanvas {
  background-color: #2d2d2d !important;
  color: #e5e5e5 !important;
}

body.dark-theme .offcanvas-header {
  border-bottom-color: #404040 !important;
}

body.dark-theme .offcanvas-header h6 {
  color: #e5e5e5 !important;
}

body.dark-theme .offcanvas-body {
  color: #e5e5e5 !important;
}

body.dark-theme .offcanvas-body .body-title {
  color: #e5e5e5 !important;
}

body.dark-theme .offcanvas-body form fieldset {
  border-bottom-color: #404040 !important;
}

body.dark-theme .offcanvas-body .radio-buttons .item label {
  border-color: #404040 !important;
}

body.dark-theme .offcanvas-body .radio-buttons .item input:checked ~ label {
  border-color: #60a5fa !important;
}

body.dark-theme .tf-button {
  background-color: #404040 !important;
  border-color: #555 !important;
  color: #e5e5e5 !important;
}

body.dark-theme .tf-button:hover {
  background-color: #555 !important;
}
