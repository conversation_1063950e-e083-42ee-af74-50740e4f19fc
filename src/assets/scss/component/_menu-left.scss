
.section-menu-left {
    position: fixed;
    width: 256px;
    min-width: 256px;
    height: 100%;
    left: 0;
    z-index: 20;
    border-right: 0;
    padding-top: 81px;
    @include flex(center,start);
    flex-direction: column;
    flex-shrink: 0;
    @include transition3;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    &::before {
        position: absolute;
        inset: 0;
        content: '';
        background: var(--Primary);
        @include transition3();
    }
    > .box-logo {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 30;
        padding: 24px 31px 32px;
        width: 256px;
        background: var(--Primary);
        @include flex(center,space-between);
        @include transition3;
        z-index: 6;
    }
    .section-menu-left-wrap {
        @include d-flex();
        flex-direction: column;
        width: 100%;
        overflow-y: auto;
        position: relative;
        z-index: 5;
        flex-grow: 1;
        &::-webkit-scrollbar {
            width: 3px;
            width: 0px;
        }
        &::-webkit-scrollbar-thumb {
            background: var(--Note);
            border-radius: 10px;
        }
    }
    .center {
        flex-grow: 1;
        padding: 11px 0;
        width: 100%;
        .center-heading {
            padding: 0 32px;
        }
    }
    .menu-item {
        position: relative;
        a {
            padding: 13px 14px 14px 30px;
            position: relative;
            @include flex(center,start);
            gap: 16px;
            .icon {
                @include flex(center,center);
                width: 20px;
                height: 20px;
                svg path {
                    @include transition3;
                }
                i {
                    font-size: 20px;
                    color: var(--Gray);
                    @include transition3;
                }
            }
            .text {
                white-space: nowrap;
                color: var(--Gray);
                font-weight: 700;
                font-size: 14px;
                line-height: 21px;
                text-transform: capitalize;
                @include transition3;
            }
            &:hover {
                &::after,
                i,
                .text {
                    color: var(--White) !important;
                }
                svg {
                    path {
                        stroke: var(--White) !important;
                    }
                }
            }
            &.active {
                svg {
                    path {
                        stroke: unset !important;
                    }
                }
            }
        }
        &.has-children {
            position: relative;
            transition-delay: 0.3s;
            &::after {
                position: absolute;
                content: '\e943';
                top: 17.5px;
                right: 21px;
                color: var(--LightGray);
                font-size: 12px;
                font-family: $fontIcon;
                @include transition3;
                cursor: pointer;
            }
            &:hover::after {
                color: var(--White);
            }
            &.active {
                > a {
                    svg path {
                        fill: var(--White) !important;
                    }
                    i,
                    .text {
                        color: var(--White);
                    }
                }
                &::after {
                    transform: rotate(90deg);
                    color: var(--White) !important;
                }
            }
        }
        .sub-menu {
            display: none;
            overflow-x: auto;
            &::-webkit-scrollbar {
                width: 3px;
            }
            a {
                padding: 0;
                position: relative;
                .text {
                    color: var(--Gray);
                    font-size: 14px;
                    line-height: 24px;
                }
            }
        }
        &:not(:last-child) {
            margin-bottom: 2px;
        }
        .menu-item-button.active {
            position: unset;
            &::before {
                position: absolute;
                content: '';
                right: 0;
                top: 0;
                bottom: 0;
                width: 4px;
                background-color: var(--YellowGreen);
                @include transition3();
            }
            .icon i,
            .text {
                color: var(--White);
            }
        }
    }
    .sub-menu-item {
        position: relative;
        padding: 12px 0 12px 76px;
        &.active {
            a {
                .text {
                    color: var(--White);
                }
            }
        }
    }
    a {
        position: relative;
    }
    .button-show-hide {
        position: relative;
        @include flex(center,center);
        font-size: 20px;
        width: 28px;
        height: 28px;
        color: rgba(10, 10, 12, 1);
        border-radius: 4px;
        background-color: rgba(246, 246, 246, 1);
        cursor: pointer;
        transition: all 0.3s, background 0s ease;
        i {
            @include transition3;
        }
        &:hover {
            i {
                color: var(--Secondary);
            }
        }
    }
    .bottom {
        @include d-flex();
        gap: 21px;
        .content {
            padding-top: 32px;
        }
    }
}
