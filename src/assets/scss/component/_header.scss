.header-dashboard {
    position: fixed;
    top: 0;
    right: 0;
    width: calc(100% - 256px);
    padding: 20px 48px 23px 48px;
    background: var(--White);
    z-index: 19;
    transition: all 0.3s, background 0s ease;
    .wrap {
        @include flex(center,space-between);
        gap: 15px;
        .header-left {
            width: 100%;
            position: relative;
            @include d-flex;
            align-items: center;
            gap: 25px;
            > a {
                display: none;
            }
            .button-show-hide {
                position: relative;
                @include flex(center,center);
                font-size: 20px;
                width: 20px;
                height: 20px;
                color: rgba(10, 10, 12, 1);
                border-radius: 4px;
                // background-color: rgba(246, 246, 246, 1);
                cursor: pointer;
                @include transition3;
                display: none;
                i {
                    @include transition3;
                }
                &:hover {
                    i {
                        color: var(--Secondary);
                    }
                }
            }
            .box-content-search {
                position: absolute;
                top: 50px;
                left: 0;
                right: 0;
                border-radius: 14px;
                padding: 16px;
                background-color: var(--White);
                box-shadow: 0px 4px 24px 2px rgba(20, 25, 38, 0.05);
                height: 538px;
                overflow-y: scroll;
                opacity: 0;
                visibility: hidden;

                &.active {
                    top: 55px;
                    opacity: 1;
                    visibility: visible;
                }

                &::-webkit-scrollbar {
                    width: 3px;
                }
                .product-item {
                    .name {
                        a {
                            color: var(--Main-Dark);
                        }
                    }
                }
            }
            .form-search {
                max-width: 307px
            }
        }
        .header-grid {
            @include d-flex;
            align-items: center;
            gap: 19px;
            .header-btn {
                @include d-flex;
                align-items: center;
                gap: 10px;
            }
            > .line1 {
                width: 1px;
                background: var(--LightGray);
                height: 24px;
            }
            > .setting {
                width: 24px;
                @include flex(center,center);
                font-size: 20px;
                i {
                    animation-name: spin;
                    animation-duration: 3s;
                    animation-iteration-count: infinite;
                    animation-timing-function: linear;
                }
            }
        }
        .header-item {
            @include flex(center,center);
            width: 32px;
            height: 32px;
            border-radius: 50%;
            i {
                font-size: 16px;
                color: var(--Primary);
            }
            &.country {
                > .dropdown {
                    > .dropdown-menu.show {
                        margin-top: 19px !important;
                    }
                }
            }
        }
    }
}