.variant-picker-item {
    .variant-picker-label {
        margin-bottom: 14px;
    }
    .variant-picker-values {
        display: flex;
        gap: 10px;
        align-items: center;
        input {
            position: absolute !important;
            overflow: hidden;
            width: 1px;
            height: 1px;
            margin: -1px;
            padding: 0;
            border: 0;
            clip: rect(0 0 0 0);
            word-wrap: normal !important;
            &:checked + label {
                border-color: #30303080;
            }
            &:checked + label.style-text {
                background-color: var(--Secondary);
                border: 0;
                div {
                    color: var(--White);
                }
            }
        }
        label {
            width: 36px;
            height: 36px;
            text-align: center;
            padding: 5px;
            border: 1px solid transparent;
            cursor: pointer;
            font-weight: 400;
            line-height: 22.4px;
            border-radius: 50%;
            @include transition3;
            &:hover {
                border-color: #30303080;
            }
            .btn-checkbox {
                width: 100%;
                height: 100%;
                display: block;
                border-radius: 50%;
                border: 0;
            }
            .text {
                font-size: 16px;
                line-height: 19px;
            }
            &.style-text {
                min-width: 40px;
                width: 40px;
                height: 37px;
                border: 0;
                border-radius: 12px;
                background-color: var(--Surface-3);
                @include flex(center,center);
            }
        }
    }
}

.bg-color-orange {
    background-color: #FF5200;
}
.bg-color-blue {
    background-color: #2377FC;
}
.bg-color-yellow {
    background-color: #FCC141;
}
.bg-color-white {
    background-color: var(--White);
}
.bg-color-black {
    background-color: var(--Main-Dark);
}
