
// image select

.dropdown.bootstrap-select.image-select {
    width: unset;
    > button {
        padding: 8px;
        background-color: transparent;
        border: 0;
        outline: none !important;
        border-radius: 6px;
        background-color: rgb(22, 19, 37, 0.03);
        &::after {
            border: 0;
            position: absolute;
            right: 8px;
            content: '\e911';
            font-family: 'icomoon';
            font-size: 16px;
            margin: 0;
        }
        &:hover {
            color: rgba(0, 0, 0, 0.8)
        }
        .filter-option-inner-inner {
            @include flex(center,start);
            gap: 5px;
            font-size: 12px;
            font-weight: 700;
            line-height: 16px;
            img {
                width: 16px;
                height: 16px;
            }
        }
    }
    &.image-w-20 > button {
        &::after {
            right: 14px;
        }
        img {
            width: 20px !important;
            height: 20px !important;
        }
    }
    > .dropdown-menu {
        overflow: unset !important;
        padding: 8px 0px;
        border-radius: 6px;
        border: 0;
        background-color: var(--White);
        box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 18px 0px;

        ul.dropdown-menu {
            @include d-flex();
            gap: 10px;
            flex-direction: column;
            a {
                border-radius: 3px;
                &:active,
                &.active {
                    color: var(--Primary) !important;
                    background-color: unset !important;
                }
            }
            .text {
                @include flex(center,start);
                gap: 5px;
                font-size: 12px;
                font-weight: 700;
                line-height: 16px;
                img {
                    width: 16px;
                    height: 16px;
                }
            }
            > li {
                > a {
                    
                    &:hover {
                        color: rgba(0, 0, 0, 0.8);
                        background-color: unset;
                    }
                }
            }
        }
        &::after {
            position: absolute;
            content: '';
            width: 16px;
            height: 16px;
            transform: translate(-50%,-50%) rotate(45deg);
            background-color: var(--white);
            top: 0;
            left: 50%;
            z-index: 2;
        }
        &[data-popper-placement="top-start"] {
            &::after {
                display: none;
            }
            &::before {
                position: absolute;
                content: '';
                width: 16px;
                height: 16px;
                transform: translate(-50%,50%) rotate(45deg);
                background-color: var(--white);
                bottom: 00%;
                left: 50%;
                z-index: 2;
            }
        }
    }
    &.type-currencies {
        > button {
            .filter-option {
                .filter-option-inner {
                    width: 50px;
                }
            }
        }
        > .dropdown-menu {
            width: 300px;
            margin-left: calc(50% - 150px) !important;
           
        }
    }
    &.type-languages {
        > .dropdown-menu {
            width: 96px;
            margin-left: calc(50% - 48px) !important;
        }
    }
    &.style-white {
        > button {
            border: 1px solid var(--White);
            &::after {
                color: var(--White);
            }
            .filter-option {
                .filter-option-inner {
                    color: var(--White);
                }
            }
        }
    }
    &.type-1 {
        width: max-content;
        > button {
            padding: 9px 35px 9px 15px;
            background-color: transparent;
        }
    }
    &.type-2 {
        width: max-content;
        > button {
            border: 1px solid var(--LightGray);
            padding: 7px 35px 7px 15px;
            background-color: transparent;
            &::after {
                color: var(--Primary);
            }
            .filter-option {
                .filter-option-inner {
                    color: var(--Primary);
                }
            }
        }
    }
}
