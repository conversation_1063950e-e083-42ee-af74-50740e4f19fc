

@media (min-width: 1440px) {
    .login-page {
        height: 100vh;
    }
}
@media (max-width: 1440px) {
    .wg-chart-default,
    .wg-box {
        padding: 24px 15px;
    }
    
}

@media (max-width: 1200px) {

    .form-style-2 { 
        .left {
            max-width: 150px !important;
        }
    }
    .header-user {
        width: unset;
        > div {
            &:last-child {
                display: none;
            }
        }
    }
    .upload-image {
        flex-wrap: wrap;
    }
    .layout-wrap {
        &.full-width {
            .section-menu-left {
                left: 0;
                .box-logo {
                    left: 0;
                }
            }
            .section-content-right {
                .header-dashboard {
                    padding-left: 15px !important;
                }
            }
        }
        .section-menu-left {
            left: -100%;
            .box-logo {
                left: -100%;
            }
        }
        .section-content-right {
            .main-content {
                padding-left: 0 !important;
                padding-top: 56px;
                .main-content-inner {
                    padding-left: 15px;
                    padding-right: 15px;
                }
            }
            .header-dashboard {
                width: 100% !important;
                padding-top: 12px;
                padding-bottom: 12px;
                padding-left: 15px !important;
                padding-right: 15px !important;
                left: 0 !important;
                .button-show-hide {
                    @include d-flex();
                }
                .header-left {
                    h6 {
                        display: none;
                    }
                }
                .header-grid {
                    >.header-btn,
                    >.line1,
                    > .divider,
                    > .user .content,
                    > .apps,
                    > .button-dark-light,
                    > .message,
                    > .noti,
                    > .country {
                        display: none;
                    }
                }
                .wg-user .image {
                    width: 24px;
                    height: 24px;
                }
                .form-search input {
                    padding-top: 7px;
                    padding-bottom: 7px;
                    border-width: 0.5px;
                }
            }
        }
    }
    .tf-container {
        width: unset;
    }
}

@media (max-width: 1024px) {
  
}

@media (max-width: 991px) {
    .my-card-item .icon {
        position: absolute;
        top: 22px;
        right: 11px;
        bottom: unset;
    }
    .grid-account-security {
        .left {
            width: 55%;
        }
        .right {
            width: 45%;
        }
    }
    .account-security-item {
        flex-direction: column;
        .heading,
        .content {
            width: 100%;
        }
        .content {
            .content-item:not(:last-child) {
                margin-bottom: 24px;
            }
        }
    }
    .swiper-button-next, 
    .swiper-button-prev {
        display: none;
    }
    .card-details {
        .content {
            flex-wrap: wrap;
            gap: 24px;
            >div {
                width: 100% !important;
            }
            .center {
                .title {
                    margin-bottom: 20px;
                }
            }
        }
    }
}

@media (max-width: 767px) {
    .offcanvas {
        padding: 30px 20px;
        &.offcanvas-end {
            border-radius: 0 !important;
        }
        .offcanvas-body form .radio-buttons .item {
            input {
                top: 8px;
                left: 6px;
            }
            div {
                font-weight: 500;
                font-size: 12px;
            }
            label {
                height: 40px;
                padding: 10px 0 10px 34px;
            }
        }
    }
    .header-dashboard::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 0;
        right: 0;
        height: 0.1px;
        background-color: var(--Gray);
    }
    .grid-3-col {
        grid-template-columns: repeat(1, 1fr);
    }
    .sign-in-box {
        .left {
            width: 100%;
        }
        .right {
            display: none;
        }
    }
    .list-transaction-head,
    .list-crypto-head {
        display: none !important;
    }
    .table-list-crypto {
        .list-crypto-content {
            min-width: unset;
            tr {
                flex-wrap: wrap;
                gap: 6px 0px;
                padding: 20px;
                td {
                    height: unset;
                    padding: 0;
                    place-content: end;
                    [data-title]::before {
                        content: attr(data-title);
                        color: var(--Black);
                        font-weight: 700;
                        text-align: start;
                        flex: 1 1 auto;
                    }
                    &:nth-child(1) {
                        width: 30%;
                        order: 1;
                        .tf-checkbox-wrapp {
                            display: none;
                        }
                    }
                    &:nth-child(2) {
                        width: 30%;
                        order: 3;
                    }
                    &:nth-child(5),
                    &:nth-child(4) {
                        display: none;
                    }
                    &:nth-child(3) {
                        order: 2;
                        width: 70%;
                        text-align: end;
                    }
                    &:nth-child(6) {
                        order: 4;
                        width: 70%;
                        > div > svg {
                            display: none;  
                        }
                    }
                }
            }
        }
    }
    .tf-table-item::before {
        display: none;
    }
    .table-list-transaction {
        .list-transaction-content {
            min-width: unset;
            tr {
                flex-wrap: wrap;
                td {
                    height: 50px;
                    padding: 0 !important;
                    [data-title]::before {
                        content: attr(data-title);
                        color: var(--Black);
                        font-weight: 700;
                        text-align: start;
                        flex: 1 1 auto;
                    }
                    &:nth-child(1) {
                        width: 60%;
                        order: 1;
                        .tf-checkbox-wrapp {
                            display: none;
                        }
                    }
                    &:nth-child(2) {
                        width: max-content;
                        max-width: 33.33%;
                        order: 3;
                    }
                    &:nth-child(3),
                    &:nth-child(4) {
                        display: none;
                    }
                    &:nth-child(5) {
                        order: 4;
                        width: 90px;
                    }
                    &:nth-child(6) {
                        order: 2;
                        width: 40%;
                        text-align: end;
                    }
                    &:nth-child(7) {
                        order: 5;
                        width: 103px;
                        >div {
                            margin-left: unset;
                        }
                    }
                }
            }
        }
    }
    .topbar-search .form-search {
        max-width: 162px;
        input {
            padding: 9px 10px 9px 34px !important;
        }
        .button-submit {
            left: 10px;
        }
    }
    .grid-account-security {
        flex-wrap: wrap;
        .left,
        .right {
            width: 100%;
        }
    }
    .account-security-item {
        .content {
            .content-item:not(:last-child) {
                margin-bottom: 5px;
            }
        }
    }
    .login-page {
        .right {
            display: none;
        }
        .login-box.type-signup {
            gap: 22px;
        }
    }
    .form-style-1 {
        > * {
            flex-wrap: wrap;
            > * {
                &:last-child {
                    width: 100%;
                }
            }
        }
    }
    .form-style-2 {
        > * {
            flex-wrap: wrap;
        }
    }
    .wg-order-detail {
        flex-wrap: wrap;
        .right {
            max-width: unset;
        }
    }
    .wg-filter,
    .order-track {
        flex-wrap: wrap;
    }
    .form-style-2 { 
        .left {
            max-width: unset !important;
        }
    } 
}

@media (max-width: 600px) {
    .layout-wrap .section-content-right .bottom-page {
        flex-direction: column;
    }
    .tf-section-4 {
        > div {
            grid-column: span 4 / span 4 !important;
        }
    }
    .w-half {
        width: 100% !important;
    }
    form {
        .cols,
        .cols-lg {
            flex-wrap: wrap;
            gap: 0 !important;
            >* {
                max-width: 100% !important;
                width: 100% !important;
            }
        }
    }
    .flex-wrap-mobile {
        flex-wrap: wrap;
    }
    .road-map {
        flex-wrap: wrap;
        gap: 30px;
        .road-map-item {
            &::before {
                display: none;
            }
        }
    }
    .mobile-wrap {
        flex-wrap: wrap;
    }
    .wrap-login-page {
        padding-top: 30px;
        padding-left: 15px;
        padding-right: 15px;
    }
    .upload-image {
        .item {
            width: 100%;
            max-width: 100%;
            max-height: unset;
        }
    }
}