<template>
  <v-app>
    <!-- Navigation Drawer -->
    <v-navigation-drawer
      v-model="drawer"
      :rail="isRailMode"
      :permanent="!isMobile"
      :temporary="isMobile"
      :width="drawerWidth"
      :rail-width="railWidth"
      color="surface"
      class="main-navigation-drawer"
      @click:outside="handleOutsideClick"
    >
      <!-- Logo Section -->
      <v-list-item class="logo-section pa-4">
        <template #prepend>
          <v-img
            :src="logoSrc"
            :alt="appName"
            max-width="154"
            max-height="40"
            contain
            class="logo-image"
          />
        </template>

        <template #append v-if="!isRailMode">
          <v-btn
            icon="mdi-menu"
            variant="text"
            size="small"
            @click="toggleRail"
            class="toggle-btn"
          />
        </template>
      </v-list-item>

      <v-divider />

      <!-- Navigation Menu -->
      <v-list
        density="compact"
        nav
        class="navigation-menu"
      >
        <v-list-subheader v-if="!isRailMode" class="text-uppercase text-caption font-weight-medium">
          Navigation
        </v-list-subheader>

        <!-- Dashboard -->
        <v-list-item
          :to="'/'"
          prepend-icon="mdi-view-dashboard"
          title="Dashboard"
          value="dashboard"
          class="nav-item"
        />

        <!-- Home -->
        <v-list-item
          :to="'/'"
          prepend-icon="mdi-home"
          title="Home"
          value="home"
          class="nav-item"
        />

        <!-- Vuetify Home -->
        <v-list-item
          :to="'/vuetify-home'"
          prepend-icon="mdi-home-variant"
          title="Vuetify Home"
          value="vuetify-home"
          class="nav-item"
        />

        <!-- Admin Panel (with permission check) -->
        <CanAccess action="read" subject="AdminPanel">
          <v-list-item
            :to="'/admin'"
            prepend-icon="mdi-shield-account"
            title="Admin Panel"
            value="admin"
            class="nav-item"
          />
        </CanAccess>

        <!-- Vuetify Admin Panel -->
        <CanAccess action="read" subject="AdminPanel">
          <v-list-item
            :to="'/vuetify-admin'"
            prepend-icon="mdi-shield-account-variant"
            title="Vuetify Admin"
            value="vuetify-admin"
            class="nav-item"
          />
        </CanAccess>

        <!-- User Dashboard -->
        <v-list-item
          :to="'/dashboard'"
          prepend-icon="mdi-account-circle"
          title="User Dashboard"
          value="user-dashboard"
          class="nav-item"
        />

        <!-- My Wallet (with submenu) -->
        <v-list-group value="wallet">
          <template #activator="{ props }">
            <v-list-item
              v-bind="props"
              prepend-icon="mdi-wallet"
              title="My Wallet"
              class="nav-item"
            />
          </template>

          <v-list-item
            :to="'/wallet'"
            title="Wallet Overview"
            value="wallet-overview"
            class="nav-subitem"
          />
          <v-list-item
            :to="'/account'"
            title="Account Settings"
            value="account-settings"
            class="nav-subitem"
          />
          <v-list-item
            :to="'/transactions'"
            title="Transaction History"
            value="transaction-history"
            class="nav-subitem"
          />
          <v-list-item
            :to="'/payment-methods'"
            title="Payment Methods"
            value="payment-methods"
            class="nav-subitem"
          />
        </v-list-group>

        <!-- Transaction -->
        <v-list-item
          :to="'/transaction'"
          prepend-icon="mdi-chart-line"
          title="Transaction"
          value="transaction"
          class="nav-item"
        />

        <!-- Crypto -->
        <v-list-item
          :to="'/crypto'"
          prepend-icon="mdi-bitcoin"
          title="Crypto"
          value="crypto"
          class="nav-item"
        />

        <!-- Exchange -->
        <v-list-item
          :to="'/exchange'"
          prepend-icon="mdi-swap-horizontal"
          title="Exchange"
          value="exchange"
          class="nav-item"
        />

        <!-- Settings -->
        <CanAccess action="read" subject="Settings">
          <v-list-item
            :to="'/settings'"
            prepend-icon="mdi-cog"
            title="Settings"
            value="settings"
            class="nav-item"
          />
        </CanAccess>

        <!-- About -->
        <v-list-item
          :to="'/about'"
          prepend-icon="mdi-information"
          title="About"
          value="about"
          class="nav-item"
        />

        <!-- Vuetify About -->
        <v-list-item
          :to="'/vuetify-about'"
          prepend-icon="mdi-information-variant"
          title="Vuetify About"
          value="vuetify-about"
          class="nav-item"
        />

        <!-- Vuetify Demo -->
        <v-list-item
          :to="'/vuetify-demo'"
          prepend-icon="mdi-star"
          title="Vuetify Demo"
          value="vuetify-demo"
          class="nav-item"
        />
      </v-list>

      <!-- Bottom Upgrade Section -->
      <template #append v-if="!isRailMode">
        <v-card
          class="ma-4 upgrade-card"
          color="primary"
          variant="flat"
        >
          <v-card-text class="text-center pa-3">
            <v-img
              src="/images/item/bot.png"
              max-width="60"
              class="mx-auto mb-2"
            />
            <div class="text-caption text-white mb-1">For more features</div>
            <div class="text-caption font-weight-bold text-white">Upgrade to Pro</div>
          </v-card-text>
        </v-card>
      </template>
    </v-navigation-drawer>

    <!-- App Bar -->
    <v-app-bar
      :order="0"
      color="surface"
      elevation="1"
      class="main-app-bar"
    >
      <!-- Mobile menu button -->
      <v-app-bar-nav-icon
        v-if="isMobile"
        @click="drawer = !drawer"
      />

      <!-- Desktop toggle button -->
      <v-btn
        v-else
        icon="mdi-menu"
        variant="text"
        @click="toggleRail"
        class="me-2"
      />

      <!-- Page Title -->
      <v-app-bar-title class="text-h6 font-weight-medium">
        {{ pageTitle }}
      </v-app-bar-title>

      <v-spacer />

      <!-- Header Actions -->
      <div class="d-flex align-center">
        <!-- Dark/Light Mode Toggle -->
        <v-btn
          :icon="themeStore.isDarkMode ? 'mdi-weather-sunny' : 'mdi-weather-night'"
          variant="text"
          @click="toggleTheme"
          class="me-2"
        />

        <!-- Notifications -->
        <v-menu offset-y>
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              icon
              variant="text"
              class="me-2"
            >
              <v-badge
                content="1"
                color="error"
                offset-x="2"
                offset-y="2"
              >
                <v-icon>mdi-bell</v-icon>
              </v-badge>
            </v-btn>
          </template>
          <v-card min-width="300">
            <v-card-title class="text-subtitle-1">Notifications</v-card-title>
            <v-divider />
            <v-card-text>
              <div class="text-center text-grey">
                No new notifications
              </div>
            </v-card-text>
          </v-card>
        </v-menu>

        <!-- Messages -->
        <v-menu offset-y>
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              icon
              variant="text"
              class="me-2"
            >
              <v-badge
                content="1"
                color="primary"
                offset-x="2"
                offset-y="2"
              >
                <v-icon>mdi-message</v-icon>
              </v-badge>
            </v-btn>
          </template>
          <v-card min-width="300">
            <v-card-title class="text-subtitle-1">Messages</v-card-title>
            <v-divider />
            <v-card-text>
              <div class="text-center text-grey">
                No new messages
              </div>
            </v-card-text>
          </v-card>
        </v-menu>

        <!-- Fullscreen Toggle -->
        <v-btn
          icon="mdi-fullscreen"
          variant="text"
          @click="toggleFullscreen"
          class="me-2"
        />

        <!-- User Menu -->
        <v-menu offset-y>
          <template #activator="{ props }">
            <v-btn
              v-bind="props"
              variant="text"
              class="user-menu-btn"
            >
              <v-avatar size="32" class="me-2">
                <v-img :src="userAvatar" alt="User Avatar" />
              </v-avatar>
              <div class="d-none d-sm-flex flex-column align-start">
                <div class="text-body-2 font-weight-medium">{{ userName }}</div>
                <div class="text-caption text-grey">{{ userRole }}</div>
              </div>
              <v-icon class="ms-1">mdi-chevron-down</v-icon>
            </v-btn>
          </template>

          <v-list density="compact" min-width="200">
            <v-list-item
              prepend-icon="mdi-account"
              title="Account"
              :to="'/profile'"
            />
            <v-list-item
              prepend-icon="mdi-receipt"
              title="Transaction"
              :to="'/transactions'"
            />
            <CanAccess action="read" subject="Settings">
              <v-list-item
                prepend-icon="mdi-cog"
                title="Settings"
                :to="'/settings'"
              />
            </CanAccess>
            <CanAccess action="read" subject="AdminPanel">
              <v-list-item
                prepend-icon="mdi-shield-account"
                title="Admin Panel"
                :to="'/admin'"
              />
            </CanAccess>
            <v-list-item
              prepend-icon="mdi-bitcoin"
              title="Crypto"
              :to="'/crypto'"
            />
            <v-divider />
            <v-list-item
              prepend-icon="mdi-logout"
              title="Log out"
              @click="handleLogout"
            />
          </v-list>
        </v-menu>
      </div>
    </v-app-bar>

    <!-- Main Content -->
    <v-main class="main-content-area">
      <v-container fluid class="pa-6">
        <slot />
      </v-container>
    </v-main>

    <!-- Theme Switcher -->
    <VuetifySwitcher />
  </v-app>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useDisplay } from 'vuetify'
import { useAuth } from '@/composables/useAuth'
import { useThemeStore } from '@/stores/theme'
import CanAccess from '@/components/auth/CanAccess.vue'
import VuetifySwitcher from '@/components/shared/VuetifySwitcher.vue'

// Composables
const router = useRouter()
const route = useRoute()
const { mobile } = useDisplay()
const { user, userName, userRole, logout } = useAuth()
const themeStore = useThemeStore()

// Reactive data
const drawer = ref(true)
const rail = ref(false)

// Computed properties
const isMobile = computed(() => mobile.value)
const isRailMode = computed(() => {
  if (isMobile.value) return false
  return rail.value || themeStore.isIconHoverMenu || themeStore.isIconDefaultMenu
})

const drawerWidth = computed(() => {
  if (themeStore.isBoxedLayout) return 256
  return 256
})

const railWidth = computed(() => 75)

const appName = computed(() => 'Dashboard App')
const logoSrc = computed(() => {
  if (themeStore.isDarkMode) {
    return '/images/logo/logo-dark.svg'
  }
  return '/images/logo/logo.svg'
})

const userAvatar = computed(() => '/images/avatar/user-1.png')

const pageTitle = computed(() => {
  const routeMeta = route.meta
  if (routeMeta?.title) {
    return routeMeta.title
  }

  switch (route.name) {
    case 'home': return 'Home'
    case 'dashboard': return 'Dashboard'
    case 'admin': return 'Admin Panel'
    case 'profile': return 'Profile'
    case 'about': return 'About'
    case 'vuetify-demo': return 'Vuetify Demo'
    case 'vuetify-home': return 'Vuetify Home'
    case 'vuetify-admin': return 'Vuetify Admin'
    case 'vuetify-about': return 'Vuetify About'
    case 'vuetify-login': return 'Vuetify Login'
    default: return 'Dashboard'
  }
})

// Methods
const toggleRail = () => {
  rail.value = !rail.value
}

const handleOutsideClick = () => {
  if (isMobile.value) {
    drawer.value = false
  }
}

const toggleTheme = () => {
  if (themeStore.isDarkMode) {
    themeStore.applyLightTheme()
  } else {
    themeStore.applyDarkTheme()
  }
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

const handleLogout = async () => {
  await logout()
  router.push('/login')
}

// Lifecycle
onMounted(() => {
  // Initialize drawer state based on screen size
  drawer.value = !isMobile.value

  // Apply theme-based rail mode
  if (themeStore.isIconHoverMenu || themeStore.isIconDefaultMenu) {
    rail.value = true
  }
})
</script>

<style scoped>
.main-navigation-drawer {
  border-right: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.logo-section {
  min-height: 64px;
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.logo-image {
  transition: all 0.3s ease;
}

.navigation-menu {
  flex: 1;
}

.nav-item {
  margin: 2px 8px;
  border-radius: 8px;
}

.nav-subitem {
  margin: 1px 16px;
  border-radius: 6px;
}

.upgrade-card {
  border-radius: 12px;
}

.main-app-bar {
  border-bottom: 1px solid rgba(var(--v-border-color), var(--v-border-opacity));
}

.user-menu-btn {
  text-transform: none;
  height: auto;
  padding: 8px 12px;
}

.main-content-area {
  background-color: rgb(var(--v-theme-background));
}

/* Rail mode adjustments */
.v-navigation-drawer--rail .logo-section {
  justify-content: center;
}

.v-navigation-drawer--rail .upgrade-card {
  display: none;
}

/* Theme-specific styles */
:global(.v-theme--dark) .main-navigation-drawer {
  background-color: rgb(var(--v-theme-surface));
}

:global(.v-theme--dark) .main-app-bar {
  background-color: rgb(var(--v-theme-surface));
}
</style>
