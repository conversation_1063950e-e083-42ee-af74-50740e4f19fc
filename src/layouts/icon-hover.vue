<script setup lang="ts">
// Icon Hover Layout Component
// This represents the collapsed sidebar that expands on hover
// CSS classes applied: menu-style-icon
</script>

<template>
  <!-- Icon Hover Layout - Collapsed sidebar that expands on hover -->
  <div class="layout-info">
    <h3>Icon Hover Layout</h3>
    <p>Collapsed sidebar that expands to full width on hover.</p>
    <div class="css-info">
      <strong>CSS Classes Applied:</strong> <code>menu-style-icon</code>
    </div>
    <div class="content-area">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.layout-info {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.css-info {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.css-info code {
  background: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.content-area {
  margin-top: 1rem;
}
</style>
