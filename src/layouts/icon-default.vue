<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuth } from '@/composables/useAuth'

const collapsed = ref(false)
const router = useRouter()
const route = useRoute()
const { logout } = useAuth()

// Navigation items
const navigationItems = [
  {
    title: 'Dashboard',
    icon: 'icon-category',
    active: true,
    hasChildren: true,
    subItems: [
      { title: 'Default', href: '/dashboard', active: false },
      { title: 'Boxed', href: '/dashboard-boxed', active: false },
      { title: 'Icon Menu', href: '/dashboard-icon-hover', active: false },
      { title: 'Icon & Text', href: '/dashboard-icon-default', active: true }
    ]
  },
  {
    title: 'My Wallet',
    icon: 'icon-wallet1',
    hasChildren: true,
    subItems: [
      { title: 'My Wallet Overview', href: '/my-wallet', active: false },
      { title: 'Account Settings', href: '/account', active: false },
      { title: 'Transaction History', href: '/transactions', active: false },
      { title: 'Payment Methods', href: '/payment-methods', active: false }
    ]
  },
  {
    title: 'Transaction',
    icon: 'transaction-icon',
    href: '/transaction',
    svg: true
  },
  {
    title: 'Crypto',
    icon: 'icon-dash1',
    href: '/crypto'
  },
  {
    title: 'Exchange',
    icon: 'icon-arrow-swap',
    href: '/exchange'
  },
  {
    title: 'Settings',
    icon: 'icon-setting1',
    href: '/settings'
  },
  {
    title: 'Component',
    icon: 'icon-search-normal1',
    href: '/component'
  }
]

const handleLogout = async () => {
  await logout()
  router.push('/login')
}

onMounted(() => {
  // Apply the icon-default class to layout-wrap
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    layoutWrap.classList.add('menu-style-icon-default')
    console.log('Applied menu-style-icon-default class to layout')
  }
})
</script>

<template>
  <!-- Icon Default Layout with exact HTML structure -->
  <div class="layout-wrap menu-style-icon-default">
    <div class="section-menu-left">
      <div class="box-logo">
        <a href="/" id="site-logo-inner">
          <img
            class=""
            id="logo_header"
            alt=""
            src="/images/logo/logo.svg"
            data-light="/images/logo/logo.svg"
            data-dark="/images/logo/logo-dark.svg"
          >
        </a>
        <div class="button-show-hide">
          <i class="icon-back"></i>
        </div>
      </div>

      <div class="section-menu-left-wrap">
        <div class="center">
          <div class="center-item">
            <div class="center-heading f14-regular text-Gray menu-heading mb-12">Navigation</div>
          </div>
          <div class="center-item">
            <ul class="">
              <li
                v-for="item in navigationItems"
                :key="item.title"
                :class="['menu-item', { 'has-children': item.hasChildren }]"
              >
                <a
                  :href="item.href || 'javascript:void(0);'"
                  :class="['menu-item-button', { active: item.active }]"
                >
                  <div class="icon">
                    <i v-if="!item.svg" :class="item.icon"></i>
                    <svg v-else class="" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M6.1428 8.50146V14.2182" stroke="#A4A4A9" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M10.0317 5.76562V14.2179" stroke="#A4A4A9" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path d="M13.8572 11.522V14.2178" stroke="#A4A4A9" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M13.9047 1.6665H6.0952C3.37297 1.6665 1.66663 3.59324 1.66663 6.3208V13.6789C1.66663 16.4064 3.36504 18.3332 6.0952 18.3332H13.9047C16.6349 18.3332 18.3333 16.4064 18.3333 13.6789V6.3208C18.3333 3.59324 16.6349 1.6665 13.9047 1.6665Z" stroke="#A4A4A9" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                  </div>
                  <div class="text">{{ item.title }}</div>
                  <div v-if="item.hasChildren" class="arrow">
                    <i class="icon-chevron-right"></i>
                  </div>
                </a>

                <ul v-if="item.hasChildren" class="sub-menu">
                  <li
                    v-for="subItem in item.subItems"
                    :key="subItem.title"
                    :class="['sub-menu-item', { active: subItem.active }]"
                  >
                    <a :href="subItem.href" class="">
                      <div class="text">{{ subItem.title }}</div>
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </div>

        <div class="bottom">
          <div class="image">
            <img src="/images/item/bot.png" alt="">
          </div>
          <div class="content">
            <p class="f12-regular text-White">For more features</p>
            <p class="f12-bold text-White">Upgrade to Pro</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Header -->
    <div class="section-content-right">
      <div class="header-dashboard">
        <div class="wrap">
          <div class="header-left">
            <a href="/" class="logo">
              <img src="/images/logo/logo.svg" alt="Logo">
            </a>
          </div>
          <div class="header-grid">
            <div class="header-item button-dark-light">
              <i class="icon-moon"></i>
            </div>
            <div class="popup-wrap noti type-header">
              <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown" aria-expanded="false">
                  <span class="header-item">
                    <span class="text-tiny">1</span>
                    <i class="icon-bell"></i>
                  </span>
                </button>
              </div>
            </div>
            <div class="popup-wrap message type-header">
              <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton2" data-bs-toggle="dropdown" aria-expanded="false">
                  <span class="header-item">
                    <span class="text-tiny">1</span>
                    <i class="icon-message-square"></i>
                  </span>
                </button>
              </div>
            </div>
            <div class="header-item button-zoom-maximize">
              <i class="icon-maximize"></i>
            </div>
            <div class="popup-wrap user type-header">
              <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton3" data-bs-toggle="dropdown" aria-expanded="false">
                  <span class="header-user wg-user">
                    <span class="image">
                      <img src="/images/avatar/user-1.png" alt="">
                    </span>
                    <span class="flex flex-column">
                      <span class="body-title mb-2">Kristin Watson</span>
                      <span class="text-tiny">Admin</span>
                    </span>
                  </span>
                </button>
                <ul class="dropdown-menu dropdown-menu-end has-content" aria-labelledby="dropdownMenuButton3">
                  <li>
                    <a href="#" class="user-item">
                      <div class="icon">
                        <i class="icon-user"></i>
                      </div>
                      <div class="body-title-2">Account</div>
                    </a>
                  </li>
                  <li>
                    <a href="#" @click="handleLogout" class="user-item">
                      <div class="icon">
                        <i class="icon-log-out"></i>
                      </div>
                      <div class="body-title-2">Log out</div>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="main-content">
        <div class="main-content-inner">
          <div class="main-content-wrap">
            <div class="flex items-center flex-wrap justify-between gap20 mb-27">
              <h3>Icon Default Layout Demo</h3>
              <ul class="breadcrumbs flex items-center flex-wrap justify-start gap10">
                <li>
                  <a href="/">
                    <div class="text-tiny">Dashboard</div>
                  </a>
                </li>
                <li>
                  <i class="icon-chevron-right"></i>
                </li>
                <li>
                  <div class="text-tiny">Icon Default Layout</div>
                </li>
              </ul>
            </div>

            <div class="wg-box">
              <div class="flex items-center justify-between gap10 flex-wrap">
                <div class="wg-filter-wrapper">
                  <h5>Icon Default Layout Test</h5>
                </div>
              </div>
              <div class="wg-table-responsive">
                <div class="layout-demo-content">
                  <h4>This page demonstrates the Icon Default Layout</h4>
                  <p><strong>Expected Behavior:</strong></p>
                  <ul>
                    <li>✅ Sidebar width: 75px (fixed)</li>
                    <li>✅ Only icons visible in sidebar</li>
                    <li>✅ No text visible in menu items</li>
                    <li>✅ No hover expansion</li>
                    <li>✅ No dark background on hover</li>
                    <li>✅ Main content starts at 75px from left</li>
                  </ul>

                  <div class="test-instructions">
                    <h5>Test Instructions:</h5>
                    <ol>
                      <li>Look at the sidebar - it should be exactly 75px wide</li>
                      <li>Hover over the sidebar - it should NOT expand</li>
                      <li>Check that only icons are visible, no text</li>
                      <li>Verify no dark background appears on hover</li>
                      <li><strong>🔍 CRITICAL TEST: Hover over "My Wallet" icon</strong> - Should show NO submenu (4 items should stay hidden)</li>
                      <li><strong>Compare with Icon Hover layout</strong> - In icon-hover, "My Wallet" would show submenu on hover</li>
                    </ol>
                  </div>

                  <div class="debug-info">
                    <h5>Debug Information:</h5>
                    <p>This layout uses your exact HTML structure with proper CSS overrides.</p>
                    <p>If you still see issues, check the browser console for any CSS conflicts.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <slot />
      </div>
    </div>
  </div>
</template>

<style>
/* CRITICAL: Override the exact problematic styles from styles.css lines 5606-5622 */

/* Override the hover expansion from styles.css line 5607 */
.layout-wrap.menu-style-icon-default .section-menu-left:hover {
  width: 75px !important; /* Override: was 256px */
  background-color: transparent !important;
  box-shadow: none !important;
  align-items: start;
}

/* Override the box-logo expansion from styles.css line 5613 */
.layout-wrap.menu-style-icon-default .section-menu-left:hover > .box-logo {
  width: 75px !important; /* Override: was 256px */
}

/* Override the ::before expansion from styles.css line 5616 */
.layout-wrap.menu-style-icon-default .section-menu-left:hover::before {
  width: 75px !important; /* Override: was 256px */
}

/* CRITICAL: Override the dark background from styles.css line 5619 */
.layout-wrap.menu-style-icon-default .section-menu-left:hover .center {
  background-color: transparent !important; /* Override: was var(--Primary) */
  width: 75px !important; /* Override: was 75px but with flex-grow: 1 */
  flex-grow: 0 !important; /* Override: was flex-grow: 1 */
}

/* Base styles for icon-default */
.layout-wrap.menu-style-icon-default .section-menu-left {
  width: 75px !important;
  min-width: 75px !important;
  max-width: 75px !important;
}

.layout-wrap.menu-style-icon-default .section-menu-left .center {
  width: 75px !important;
  background-color: transparent !important;
  flex-grow: 0 !important;
}

/* Hide text elements and arrows */
.layout-wrap.menu-style-icon-default .section-menu-left .center-item .menu-item > a > .text {
  display: none !important;
}

.layout-wrap.menu-style-icon-default .section-menu-left .center-item .menu-item > a > .arrow {
  display: none !important;
}

.layout-wrap.menu-style-icon-default .section-menu-left .center-item .menu-item::after {
  display: none !important;
}

/* Hide center heading */
.layout-wrap.menu-style-icon-default .section-menu-left .center-heading {
  display: none !important;
}

/* Hide bottom section */
.layout-wrap.menu-style-icon-default .section-menu-left .bottom {
  display: none !important;
}

/* Prevent submenu expansion on hover */
.layout-wrap.menu-style-icon-default .section-menu-left .center-item .menu-item.has-children:hover .sub-menu {
  opacity: 0 !important;
  visibility: hidden !important;
  display: none !important;
}

/* Prevent background color change on menu items */
.layout-wrap.menu-style-icon-default .section-menu-left .center-item .menu-item.has-children:hover > a {
  background-color: transparent !important;
}

/* Header and content positioning */
.layout-wrap.menu-style-icon-default .header-dashboard {
  width: calc(100% - 75px) !important;
}

.layout-wrap.menu-style-icon-default .main-content {
  padding-left: 75px !important;
}

/* Box logo styling */
.layout-wrap.menu-style-icon-default .section-menu-left > .box-logo {
  width: 75px !important;
  padding: 15px;
}

.layout-wrap.menu-style-icon-default .section-menu-left > .box-logo a img {
  min-width: 154px;
  width: 154px;
}

.layout-wrap.menu-style-icon-default .section-menu-left > .box-logo .button-show-hide {
  display: none;
}

/* Demo content styling */
.layout-demo-content {
  padding: 20px;
}

.layout-demo-content h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.layout-demo-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.layout-demo-content li {
  margin: 5px 0;
}

.test-instructions,
.debug-info {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  border-left: 4px solid #007bff;
}

.test-instructions h5,
.debug-info h5 {
  margin-top: 0;
  color: #007bff;
}

.test-instructions ol {
  margin: 10px 0;
  padding-left: 20px;
}
</style>
