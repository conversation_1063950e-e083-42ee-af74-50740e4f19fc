<script setup lang="ts">
// Boxed Layout Component
// This represents the boxed layout with constrained width
// CSS classes applied: layout-width-boxed
</script>

<template>
  <!-- Boxed Layout - Constrained width layout with centered content -->
  <div class="layout-info">
    <h3>Boxed Layout</h3>
    <p>Constrained width layout with centered content (max-width: 1440px).</p>
    <div class="css-info">
      <strong>CSS Classes Applied:</strong> <code>layout-width-boxed</code>
    </div>
    <div class="content-area">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.layout-info {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.css-info {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.css-info code {
  background: #e9ecef;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.content-area {
  margin-top: 1rem;
}
</style>
