<script setup lang="ts">
// Default Layout Component
// This represents the standard layout with full sidebar
// CSS classes applied: none (uses base layout-wrap styles)
</script>

<template>
  <!-- Default Layout - Standard full sidebar layout -->
  <div class="layout-info">
    <h3>Default Layout</h3>
    <p>Standard full sidebar with complete navigation menu visible.</p>
    <div class="css-info">
      <strong>CSS Classes Applied:</strong> None (base layout-wrap styles)
    </div>
    <div class="content-area">
      <slot />
    </div>
  </div>
</template>

<style scoped>
.layout-info {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.css-info {
  margin: 0.5rem 0;
  font-size: 0.9rem;
}

.content-area {
  margin-top: 1rem;
}
</style>
