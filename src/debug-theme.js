// Debug script to check theme switcher status
// Run this in browser console: import('/src/debug-theme.js')

export function debugThemeSwitcher() {
  console.log('🔍 Theme Switcher Debug Report')
  console.log('================================')
  
  // Check if theme switcher button exists
  const settingButton = document.querySelector('.setting')
  console.log('⚙️ Settings button found:', !!settingButton)
  if (settingButton) {
    console.log('   - Button element:', settingButton)
    console.log('   - Button classes:', settingButton.className)
    console.log('   - Button data-bs-toggle:', settingButton.getAttribute('data-bs-toggle'))
    console.log('   - Button data-bs-target:', settingButton.getAttribute('data-bs-target'))
  }
  
  // Check if offcanvas exists
  const offcanvas = document.querySelector('#offcanvasRight')
  console.log('📋 Offcanvas panel found:', !!offcanvas)
  if (offcanvas) {
    console.log('   - Offcanvas element:', offcanvas)
    console.log('   - Offcanvas classes:', offcanvas.className)
  }
  
  // Check if header-grid exists
  const headerGrid = document.querySelector('.header-grid')
  console.log('📊 Header grid found:', !!headerGrid)
  if (headerGrid) {
    console.log('   - Header grid element:', headerGrid)
    console.log('   - Header grid children:', headerGrid.children.length)
  }
  
  // Check if Bootstrap is loaded
  const bootstrapLoaded = typeof window.bootstrap !== 'undefined'
  console.log('🅱️ Bootstrap loaded:', bootstrapLoaded)
  
  // Check current theme
  const isDarkTheme = document.body.classList.contains('dark-theme')
  console.log('🌙 Dark theme active:', isDarkTheme)
  console.log('   - Body classes:', document.body.className)
  
  // Check localStorage
  const savedTheme = localStorage.getItem('toggled')
  console.log('💾 Saved theme:', savedTheme)
  
  // Check if SwitcherPlugin is working
  const wrapperElements = document.querySelectorAll('#wrapper, #app, .layout-wrap')
  console.log('🎯 Wrapper elements found:', wrapperElements.length)
  wrapperElements.forEach((el, i) => {
    console.log(`   - Wrapper ${i + 1}:`, el.tagName, el.id || el.className)
  })
  
  console.log('================================')
  
  return {
    settingButton: !!settingButton,
    offcanvas: !!offcanvas,
    headerGrid: !!headerGrid,
    bootstrapLoaded,
    isDarkTheme,
    savedTheme,
    wrapperElements: wrapperElements.length
  }
}

// Auto-run debug when loaded
if (typeof window !== 'undefined') {
  setTimeout(debugThemeSwitcher, 1000)
}

// Manual test functions
export function manualToggleDark() {
  const body = document.body
  if (body.classList.contains('dark-theme')) {
    body.classList.remove('dark-theme')
    localStorage.setItem('toggled', 'light-theme')
    console.log('✅ Switched to light theme')
  } else {
    body.classList.add('dark-theme')
    localStorage.setItem('toggled', 'dark-theme')
    console.log('✅ Switched to dark theme')
  }
}

export function manualInjectOffcanvas() {
  if (document.querySelector('#offcanvasRight')) {
    console.log('⚠️ Offcanvas already exists')
    return
  }
  
  const markup = `
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel">
      <div class="offcanvas-header">
        <h6 id="offcanvasRightLabel">Theme Settings</h6>
        <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
      </div>
      <div class="offcanvas-body">
        <p>Manual injection test - Theme switcher panel</p>
        <button onclick="document.body.classList.toggle('dark-theme')" class="btn btn-primary">Toggle Dark Theme</button>
      </div>
    </div>
  `
  
  document.body.insertAdjacentHTML('beforeend', markup)
  console.log('✅ Manually injected offcanvas')
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  window.debugThemeSwitcher = debugThemeSwitcher
  window.manualToggleDark = manualToggleDark
  window.manualInjectOffcanvas = manualInjectOffcanvas
}
