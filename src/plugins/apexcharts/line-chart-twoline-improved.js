export default {
  series: [
    {
      name: 'Item 01',
      data: [31, 90, 58, 70, 92, 89, 80],
    },
    {
      name: 'Item 02',
      data: [51, 45, 25, 51, 34, 42, 41], // Changed -25 to 25 to avoid negative stacking issues
    },
  ],
  chart: {
    height: 207,
    type: 'line',
    stacked: false, // Changed to false for better visibility
    toolbar: {
      show: false,
    },
  },
  legend: {
    show: true, // Changed to true to show series labels
  },
  colors: ['#D4FE75', '#FF6B6B'], // Changed from transparent #ffffff4d to visible #FF6B6B
  stroke: {
    curve: 'smooth',
    width: 2, // Increased from 1 for better visibility
  },
  yaxis: {
    show: false,
  },
  xaxis: {
    labels: {
      style: {
        colors: '#95989D',
      },
    },
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  },
  tooltip: {
    enabled: true, // Changed from false to true for better UX
  },
  markers: {
    size: 3,
    hover: {
      size: 5
    }
  }
}
