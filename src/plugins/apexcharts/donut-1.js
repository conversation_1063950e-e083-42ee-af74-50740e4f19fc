export default {
  series: [56, 15, 56, 56],
  chart: {
    type: 'donut',
    height: 180,
  },
  labels: ['Grocery', 'Shopping', 'Health', 'Rent'],
  colors: ['#90caf9', '#f4ff81', '#ce93d8', '#ba68c8'],
  legend: {
    show: false,
  },
  dataLabels: {
    enabled: false,
  },
  plotOptions: {
    pie: {
      donut: {
        size: '45%',
      },
    },
  },
  tooltip: {
    y: {
      formatter: function (value) {
        return value + '%'
      },
    },
  },
}
