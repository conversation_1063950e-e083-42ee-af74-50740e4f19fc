// This module exports all UI enhancement functions
// so they can be imported by the Vue plugin.
import $ from 'jquery'
import 'jquery-countto'
/**
 * selectImages
 */
export function selectImages() {
  if ($('.image-select').length > 0) {
    const selectIMG = $('.image-select')
    selectIMG.find('option').each((idx, elem) => {
      const selectOption = $(elem)
      const imgURL = selectOption.attr('data-thumbnail')
      if (imgURL) {
        selectOption.attr(
          'data-content',
          "<img src='%i'/> %s".replace(/%i/, imgURL).replace(/%s/, selectOption.text()),
        )
      }
    })
    selectIMG.selectpicker()
  }
}

/**
 * menuleft
 */
export function menuleft() {
  if ($('div').hasClass('section-menu-left')) {
    const bt = $('.section-menu-left').find('.has-children')
    bt.on('click', function () {
      const args = { duration: 200 }
      if ($(this).hasClass('active')) {
        $(this).children('.sub-menu').slideUp(args)
        $(this).removeClass('active')
      } else {
        $('.sub-menu').slideUp(args)
        $(this).children('.sub-menu').slideDown(args)
        $('.menu-item.has-children').removeClass('active')
        $(this).addClass('active')
      }
    })
    $('.sub-menu-item').on('click', function (event) {
      event.stopPropagation()
    })
  }
}

/**
 * tabs
 */
export function tabs() {
  $('.widget-tabs').each(function () {
    $(this).find('.widget-content-tab').children().hide()
    $(this).find('.widget-content-tab').children('.active').show()
    $(this)
      .find('.widget-menu-tab')
      .find('li')
      .on('click', function () {
        const liIndex = $(this).index()
        const parent = $(this).parents('.widget-tabs')
        parent.find('.widget-menu-tab li').removeClass('active')
        $(this).addClass('active')
        parent
          .find('.widget-content-tab')
          .children()
          .removeClass('active')
          .eq(liIndex)
          .addClass('active')
          .fadeIn('slow')
          .siblings()
          .hide()
      })
  })
}

// prevent dropdown from closing on click
export function dropdownFix() {
  $('ul.dropdown-menu.has-content').on('click', function (e) {
    e.stopPropagation()
  })
  $('.button-close-dropdown').on('click', function () {
    $(this).closest('.dropdown').find('.dropdown-toggle').removeClass('show')
    $(this).closest('.dropdown').find('.dropdown-menu').removeClass('show')
  })
}

/**
 * collapse_menu
 */
export function collapse_menu() {
  // only hook the sidebar’s toggle button
  $('.section-menu-left .button-show-hide').on('click', function () {
    $('.layout-wrap').toggleClass('full-width')
  })
}

/**
 * showpass
 */
export function showpass() {
  $('.show-pass').on('click', function () {
    $(this).toggleClass('active')
    const input = $(this).parents('.password').find('.password-input')
    input.attr('type', input.attr('type') === 'password' ? 'text' : 'password')
  })
}

/**
 * select_colors_theme
 */
export function select_colors_theme() {
  if ($('div').hasClass('select-colors-theme')) {
    $('.select-colors-theme .item').on('click', function () {
      $(this).parents('.select-colors-theme').find('.active').removeClass('active')
      $(this).toggleClass('active')
    })
  }
}

/**
 * icon_function
 */
export function icon_function() {
  if ($('div').hasClass('list-icon-function')) {
    $('.list-icon-function .trash').on('click', function () {
      $(this).parents('.item-row').remove()
    })
  }
}

/**
 * box_search
 */
export function box_search() {
  $(document).on('click', function (e) {
    if (e.target.id !== 's') {
      $('.box-content-search').removeClass('active')
    }
  })
  $(document).on('click', function (e) {
    if (e.target.class !== 'a111') {
      $('.show-search').removeClass('active')
    }
  })
  $('.show-search').on('click', function (e) {
    e.stopPropagation()
  })
  $('.search-form').on('click', function (e) {
    e.stopPropagation()
  })
  const input = $('.header-dashboard').find('.form-search input')
  input.on('input', function () {
    $(this).val().trim() !== ''
      ? $('.box-content-search').addClass('active')
      : $('.box-content-search').removeClass('active')
  })
}

/**
 * variant_picker
 */
export function variant_picker() {
  if ($('.variant-picker-item').length) {
    $('.variant-picker-item label').on('click', function () {
      $(this)
        .closest('.variant-picker-item')
        .find('.variant-picker-label-value')
        .text($(this).data('value'))
    })
  }
}

/**
 * fullcheckbox
 */
export function fullcheckbox() {
  $('.total-checkbox').on('click', function () {
    const isChecked = $(this).is(':checked')
    const wrap = $(this).closest('.wrap-checkbox')
    wrap.find('.tf-table-item').toggleClass('checked', isChecked)
    wrap.find('.checkbox-item').prop('checked', isChecked)
  })
  $('.tf-table-item .checkbox-item').on('click', function () {
    $(this).closest('.tf-table-item').toggleClass('checked')
  })
}

/**
 * counter
 */
export function counter() {
  if ($(document.body).hasClass('counter-scroll')) {
    let triggered = false
    const elements = $('.counter')
    const oTop = elements.offset().top - window.innerHeight
    const runCount = () => {
      if ($().countTo) {
        elements.find('.number').each(function () {
          const to = $(this).data('to')
          const speed = $(this).data('speed')
          $(this).countTo({ to, speed })
        })
      }
    }
    if (!triggered && window.scrollY > oTop) {
      runCount()
      triggered = true
    }
    $(window).on('scroll', function () {
      if (!triggered && window.scrollY > oTop) {
        runCount()
        triggered = true
      }
    })
  }
}

/**
 * preloader
 */
export function preloader() {
  setTimeout(() => {
    $('#preload').fadeOut('slow', function () {
      $(this).remove()
    })
  }, 500)
}
