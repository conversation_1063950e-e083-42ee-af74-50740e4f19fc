// 1) CarouselPlugin: modern Swiper ESM
import Swiper from 'swiper'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'

export const CarouselPlugin = {
  install(app) {
    app.directive('carousel', {
      mounted(el, { value: opts = {} }) {
        new Swiper(el, {
          // modules: [Navigation, Pagination, Autoplay],
          ...opts,
        })
      },
    })
  },
}

// 2) CountToPlugin: jQuery + countTo
export const CountToPlugin = {
  install(app) {
    app.directive('count-to', {
      mounted(el, { value: opts = {} }) {
        // assumes window.$ is jQuery and countTo is loaded
        window.$(el).countTo(opts)
      },
    })
  },
}

// 3) SwitcherPlugin: DISABLED - using Vue Switcher component instead
// import SwitcherPlugin from './switcher.js'
// export { SwitcherPlugin }

// 4) ThemeSettingsPlugin: theme settings are now initialized by switcher.js
// This plugin is kept for potential future use but doesn't duplicate functionality
export const ThemeSettingsPlugin = {
  install(app) {
    // Theme settings are now handled by switcher.js
    console.log('ThemeSettingsPlugin: Theme settings handled by switcher.js')
  },
}

// 5) UIEnhancementsPlugin: import your jQuery helpers
import {
  selectImages,
  menuleft,
  tabs,
  collapse_menu,
  showpass,
  select_colors_theme,
  icon_function,
  box_search,
  variant_picker,
  fullcheckbox,
  counter,
  preloader,
  dropdownFix,
} from './main.js'

export const UIEnhancementsPlugin = {
  install(app) {
    app.mixin({
      mounted() {
        if (this !== this.$root) return
        selectImages()
        menuleft()
        tabs()
        collapse_menu()
        showpass()
        select_colors_theme()
        icon_function()
        box_search()
        variant_picker()
        fullcheckbox()
        counter()
        preloader()
        dropdownFix()
      },
    })
  },
}

// ChartPlugin disabled - charts are now initialized manually in components where needed
// This prevents duplicate chart rendering issues

export const ChartPlugin = {
  install(app) {
    // No-op - chart initialization is handled manually in components
  },
}

// 6) CarouselJSPlugin: use the existing carousel.js functionality
export const CarouselJSPlugin = {
  install(app) {
    app.directive('carousel-js', {
      mounted(el, { value: opts = {} }) {
        // Check if element has the tf-sw-card class or apply default carousel behavior
        if (el.classList.contains('tf-sw-card') || opts.forceInit) {
          // Ensure Swiper is available globally
          if (typeof Swiper !== 'undefined') {
            new Swiper(el, {
              autoplay: {
                delay: opts.delay || 200,
                disableOnInteraction: false,
                pauseOnMouseEnter: true,
              },
              observer: true,
              observeParents: true,
              // autoplay: true,
              slidesPerView: opts.slidesPerView || 4,
              loop: opts.loop !== false,
              spaceBetween: opts.spaceBetween || 24,
              speed: opts.speed || 600,
              breakpoints: opts.breakpoints || {
                0: {
                  slidesPerView: 1,
                },
                500: {
                  slidesPerView: 2,
                },
                768: {
                  slidesPerView: 4,
                },
              },
              ...opts,
            })
          }
        }
      },
    })

    // Also provide a mixin for automatic initialization
    app.mixin({
      mounted() {
        if (this !== this.$root) return
        // Auto-initialize any .tf-sw-card elements
        this.$nextTick(() => {
          const carouselElements = document.querySelectorAll('.tf-sw-card')
          carouselElements.forEach((el) => {
            if (!el._swiper && typeof Swiper !== 'undefined') {
              new Swiper(el, {
                autoplay: {
                  delay: 200,
                  disableOnInteraction: false,
                  pauseOnMouseEnter: true,
                },
                observer: true,
                observeParents: true,
                // autoplay: true,
                slidesPerView: 4,
                loop: true,
                spaceBetween: 24,
                speed: 600,
                breakpoints: {
                  0: {
                    slidesPerView: 1,
                  },
                  500: {
                    slidesPerView: 2,
                  },
                  768: {
                    slidesPerView: 4,
                  },
                },
              })
            }
          })
        })
      },
    })
  },
}

// 7) SwiperBundlePlugin: general-purpose Swiper plugin using swiper-bundle
export const SwiperBundlePlugin = {
  install(app) {
    app.directive('swiper', {
      mounted(el, { value: opts = {} }) {
        // Ensure Swiper is available globally (from swiper-bundle)
        if (typeof Swiper !== 'undefined') {
          const swiperInstance = new Swiper(el, {
            // Default options
            slidesPerView: 1,
            spaceBetween: 10,
            loop: false,
            autoplay: false,
            // Merge with user options
            ...opts,
          })

          // Store instance on element for potential cleanup
          el._swiperInstance = swiperInstance
        }
      },
      unmounted(el) {
        // Cleanup Swiper instance when component is unmounted
        if (el._swiperInstance) {
          el._swiperInstance.destroy(true, true)
          delete el._swiperInstance
        }
      },
    })

    // Provide global Swiper access through app config
    app.config.globalProperties.$swiper = (selector, options = {}) => {
      const element = typeof selector === 'string' ? document.querySelector(selector) : selector
      if (element && typeof Swiper !== 'undefined') {
        return new Swiper(element, options)
      }
      return null
    }
  },
}

// aggregate
export default {
  install(app) {
    app.use(CarouselPlugin)
    app.use(CountToPlugin)
    // app.use(SwitcherPlugin) // DISABLED - using Vue Switcher component
    app.use(ThemeSettingsPlugin)
    app.use(UIEnhancementsPlugin)
    app.use(ChartPlugin)
    app.use(CarouselJSPlugin)
    app.use(SwiperBundlePlugin)
  },
}
