import { abilitiesPlugin } from '@casl/vue'
import { ability } from '@/abilities'

/**
 * CASL Vue Plugin Configuration
 * This plugin provides CASL abilities to all Vue components
 */
export const CASLPlugin = {
  install(app) {
    // Install the CASL Vue plugin with our ability instance
    app.use(abilitiesPlugin, ability, {
      useGlobalProperties: true
    })

    // Make ability available globally
    app.config.globalProperties.$ability = ability
    app.provide('$ability', ability)
  }
}

export default CASLPlugin
