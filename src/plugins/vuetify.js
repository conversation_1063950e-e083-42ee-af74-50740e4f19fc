import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import { aliases, mdi } from 'vuetify/iconsets/mdi'
import '@mdi/font/css/materialdesignicons.css'
import 'vuetify/styles'

// Define custom color themes that match the current implementation
const lightTheme = {
  dark: false,
  colors: {
    // Primary colors matching current theme
    primary: '#161326',
    secondary: '#2377FC', 
    accent: '#35988D',
    error: '#FD7972',
    warning: '#ECFF79',
    info: '#AFC0FF',
    success: '#2BC155',
    
    // Surface colors
    surface: '#FFFFFF',
    'surface-variant': '#F8F8F8',
    'on-surface': '#161326',
    'on-surface-variant': '#6D6D6D',
    
    // Background colors
    background: '#FFFFFF',
    'on-background': '#161326',
    
    // Menu and header colors (will be overridden by theme system)
    'menu-background': '#161326',
    'header-background': '#FFFFFF',
    
    // Additional custom colors
    'gray-dark': '#6D6D6D',
    'gray': '#A4A4A9',
    'gainsboro': '#F8F8F8',
    'light-gray': '#D2DDDC',
  }
}

const darkTheme = {
  dark: true,
  colors: {
    // Primary colors for dark theme
    primary: '#2377FC',
    secondary: '#35988D', 
    accent: '#C388F7',
    error: '#FD7972',
    warning: '#ECFF79',
    info: '#AFC0FF',
    success: '#2BC155',
    
    // Surface colors for dark theme
    surface: '#1a1a1a',
    'surface-variant': '#2a2a2a',
    'on-surface': '#e5e5e5',
    'on-surface-variant': '#b0b0b0',
    
    // Background colors for dark theme
    background: '#1a1a1a',
    'on-background': '#e5e5e5',
    
    // Menu and header colors for dark theme
    'menu-background': '#2d2d2d',
    'header-background': '#2d2d2d',
    
    // Additional custom colors for dark theme
    'gray-dark': '#b0b0b0',
    'gray': '#8a8a8a',
    'gainsboro': '#404040',
    'light-gray': '#505050',
  }
}

export default createVuetify({
  components,
  directives,
  icons: {
    defaultSet: 'mdi',
    aliases,
    sets: {
      mdi,
    },
  },
  theme: {
    defaultTheme: 'light',
    themes: {
      light: lightTheme,
      dark: darkTheme,
    },
    variations: {
      colors: ['primary', 'secondary', 'accent'],
      lighten: 4,
      darken: 4,
    },
  },
  defaults: {
    VBtn: {
      style: 'text-transform: none;',
      variant: 'flat',
    },
    VCard: {
      elevation: 2,
    },
    VNavigationDrawer: {
      elevation: 0,
    },
    VAppBar: {
      elevation: 0,
    },
    VTabs: {
      color: 'primary',
    },
    VTab: {
      style: 'text-transform: none;',
    },
  },
  display: {
    mobileBreakpoint: 'sm',
    thresholds: {
      xs: 0,
      sm: 600,
      md: 960,
      lg: 1280,
      xl: 1920,
    },
  },
})
