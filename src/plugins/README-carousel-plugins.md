# Carousel and Swiper Plugins

This document explains how to use the newly created CarouselJSPlugin and SwiperBundlePlugin in your Vue application.

## CarouselJSPlugin

The CarouselJSPlugin wraps the existing carousel.js functionality and provides both directive-based and automatic initialization for Swiper carousels.

### Features

- **Automatic initialization**: Automatically initializes any `.tf-sw-card` elements as Swiper carousels
- **Directive support**: Use `v-carousel-js` directive for manual control
- **Configurable options**: Override default settings through directive values

### Usage

#### Automatic Initialization

Simply add the `tf-sw-card` class to any element:

```html
<div class="tf-sw-card">
  <div class="swiper-wrapper">
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
    <div class="swiper-slide">Slide 3</div>
  </div>
</div>
```

#### Directive Usage

```html
<!-- Basic usage -->
<div v-carousel-js class="tf-sw-card">
  <div class="swiper-wrapper">
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
  </div>
</div>

<!-- With custom options -->
<div v-carousel-js="{ delay: 500, slidesPerView: 3, spaceBetween: 30 }" class="tf-sw-card">
  <div class="swiper-wrapper">
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
  </div>
</div>

<!-- Force initialization on non-tf-sw-card elements -->
<div v-carousel-js="{ forceInit: true, slidesPerView: 2 }" class="my-custom-carousel">
  <div class="swiper-wrapper">
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
  </div>
</div>
```

### Default Configuration

- **Autoplay**: 200ms delay, pause on mouse enter
- **Slides per view**: 4 (responsive: 1 on mobile, 2 on tablet, 4 on desktop)
- **Loop**: Enabled
- **Space between**: 24px
- **Speed**: 600ms

## SwiperBundlePlugin

The SwiperBundlePlugin provides a general-purpose Swiper implementation using the swiper-bundle.min.js file.

### Features

- **Flexible directive**: Use `v-swiper` directive with any configuration
- **Global method**: Access `$swiper()` method from any component
- **Automatic cleanup**: Destroys Swiper instances when components unmount
- **Full Swiper API**: Access to all Swiper features and modules

### Usage

#### Directive Usage

```html
<!-- Basic swiper -->
<div v-swiper>
  <div class="swiper-wrapper">
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
  </div>
</div>

<!-- With navigation and pagination -->
<div v-swiper="{
  navigation: {
    nextEl: '.swiper-button-next',
    prevEl: '.swiper-button-prev',
  },
  pagination: {
    el: '.swiper-pagination',
    clickable: true,
  },
  slidesPerView: 3,
  spaceBetween: 30,
  loop: true
}">
  <div class="swiper-wrapper">
    <div class="swiper-slide">Slide 1</div>
    <div class="swiper-slide">Slide 2</div>
    <div class="swiper-slide">Slide 3</div>
  </div>
  <div class="swiper-pagination"></div>
  <div class="swiper-button-prev"></div>
  <div class="swiper-button-next"></div>
</div>
```

#### Global Method Usage

```javascript
// In your Vue component
export default {
  mounted() {
    // Create a swiper instance programmatically
    const mySwiper = this.$swiper('.my-swiper', {
      slidesPerView: 2,
      spaceBetween: 20,
      autoplay: {
        delay: 3000,
      }
    })
  }
}
```

### Default Configuration

- **Slides per view**: 1
- **Space between**: 10px
- **Loop**: Disabled
- **Autoplay**: Disabled

## Installation

Both plugins are automatically included when you use the default plugin export:

```javascript
import VuePlugins from '@/plugins/vue-plugins.js'

app.use(VuePlugins)
```

Or you can use them individually:

```javascript
import { CarouselJSPlugin, SwiperBundlePlugin } from '@/plugins/vue-plugins.js'

app.use(CarouselJSPlugin)
app.use(SwiperBundlePlugin)
```

## Requirements

- Swiper library must be available globally (either through swiper-bundle.min.js or modern ESM import)
- Proper Swiper CSS classes and structure in your HTML

## Notes

- The CarouselJSPlugin is specifically designed for the existing `.tf-sw-card` carousel pattern
- The SwiperBundlePlugin is more general-purpose and can be used for any Swiper implementation
- Both plugins handle cleanup and provide error checking for Swiper availability
