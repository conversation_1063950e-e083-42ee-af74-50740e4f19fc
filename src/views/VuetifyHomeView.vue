<template>
  <div class="home-view">
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center flex-wrap">
          <div>
            <h1 class="text-h4 font-weight-bold mb-2">Dashboard Overview</h1>
            <v-breadcrumbs
              :items="breadcrumbs"
              density="compact"
              class="pa-0"
            >
              <template #divider>
                <v-icon>mdi-chevron-right</v-icon>
              </template>
            </v-breadcrumbs>
          </div>
          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            variant="flat"
          >
            Add New
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Stats Cards Row -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" lg="3">
        <v-card
          color="primary"
          variant="flat"
          class="text-white stats-card"
        >
          <v-card-text>
            <div class="d-flex justify-space-between align-start">
              <div>
                <div class="text-h4 font-weight-bold mb-1">$34,57</div>
                <div class="text-body-2 mb-2">+4% (30 days)</div>
              </div>
              <v-avatar size="48" color="white" class="text-primary">
                <v-icon size="24">mdi-bitcoin</v-icon>
              </v-avatar>
            </div>
            <div class="chart-container mt-3">
              <ApexChart
                chart-id="small-chart-1-vuetify"
                chart-type="small-chart-1"
                :options="chartOptions.primary"
              />
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="3">
        <v-card variant="flat" class="stats-card">
          <v-card-text>
            <div class="d-flex justify-space-between align-start">
              <div>
                <div class="text-h4 font-weight-bold mb-1">$54,57</div>
                <div class="text-body-2 text-grey mb-2">+4% (30 days)</div>
              </div>
              <v-avatar size="48" color="success" class="text-white">
                <v-icon size="24">mdi-credit-card</v-icon>
              </v-avatar>
            </div>
            <div class="chart-container mt-3">
              <ApexChart
                chart-id="small-chart-2-vuetify"
                chart-type="small-chart-2"
                :options="chartOptions.success"
              />
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="3">
        <v-card variant="flat" class="stats-card">
          <v-card-text>
            <div class="d-flex justify-space-between align-start">
              <div>
                <div class="text-h4 font-weight-bold mb-1">$14,47</div>
                <div class="text-body-2 text-grey mb-2">+4% (30 days)</div>
              </div>
              <v-avatar size="48" color="warning" class="text-white">
                <v-icon size="24">mdi-fire</v-icon>
              </v-avatar>
            </div>
            <div class="chart-container mt-3">
              <ApexChart
                chart-id="small-chart-3-vuetify"
                chart-type="small-chart-3"
                :options="chartOptions.warning"
              />
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="3">
        <v-card
          color="success"
          variant="flat"
          class="text-white stats-card"
        >
          <v-card-text>
            <div class="d-flex justify-space-between align-start">
              <div>
                <div class="text-h4 font-weight-bold mb-1">$34,57</div>
                <div class="text-body-2 mb-2">+4% (30 days)</div>
              </div>
              <v-avatar size="48" color="white" class="text-success">
                <v-icon size="24">mdi-trending-up</v-icon>
              </v-avatar>
            </div>
            <div class="chart-container mt-3">
              <ApexChart
                chart-id="small-chart-4-vuetify"
                chart-type="small-chart-4"
                :options="chartOptions.error"
              />
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Main Content Row -->
    <v-row>
      <!-- Market Overview -->
      <v-col cols="12" lg="6">
        <v-card class="mb-6" elevation="2">
          <v-card-title class="d-flex justify-space-between align-center">
            <span>Market Overview</span>
            <v-chip-group v-model="selectedPeriod" mandatory>
              <v-chip value="week" size="small">Week</v-chip>
              <v-chip value="month" size="small">Month</v-chip>
              <v-chip value="year" size="small">Year</v-chip>
            </v-chip-group>
          </v-card-title>
          
          <v-card-text>
            <div class="d-flex justify-space-between align-center mb-4">
              <div class="d-flex gap-4">
                <div class="d-flex align-center">
                  <div class="legend-dot bg-purple me-2"></div>
                  <div class="text-body-2">
                    <span class="text-grey">Buy</span>
                    <span class="font-weight-bold ms-1">$8,420.50</span>
                  </div>
                </div>
                <div class="d-flex align-center">
                  <div class="legend-dot bg-primary me-2"></div>
                  <div class="text-body-2">
                    <span class="text-grey">Sell</span>
                    <span class="font-weight-bold ms-1">$8,420.50</span>
                  </div>
                </div>
              </div>
              <v-btn
                color="purple"
                variant="outlined"
                size="small"
                prepend-icon="mdi-download"
              >
                Get Report
              </v-btn>
            </div>
            
            <div class="chart-container">
              <div id="candlestick-1-vuetify" style="height: 300px;"></div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Crypto Statistics -->
      <v-col cols="12" lg="6">
        <v-card
          color="primary"
          variant="flat"
          class="text-white mb-6"
          elevation="2"
        >
          <v-card-title class="d-flex justify-space-between align-center">
            <span>Crypto Statistics</span>
            <v-menu>
              <template #activator="{ props }">
                <v-btn
                  v-bind="props"
                  icon="mdi-dots-vertical"
                  variant="text"
                  color="white"
                  size="small"
                />
              </template>
              <v-list>
                <v-list-item title="This Week" />
                <v-list-item title="This Day" />
              </v-list>
            </v-menu>
          </v-card-title>
          
          <v-card-text>
            <div class="d-flex gap-3 mb-4 flex-wrap">
              <v-chip
                v-for="crypto in cryptoOptions"
                :key="crypto.symbol"
                :color="crypto.active ? 'white' : 'transparent'"
                :text-color="crypto.active ? 'primary' : 'white'"
                size="small"
                variant="flat"
                @click="toggleCrypto(crypto.symbol)"
              >
                {{ crypto.symbol }}
              </v-chip>
            </div>
            
            <div class="chart-container">
              <ApexChart
                chart-id="line-chart-crypto-vuetify"
                chart-type="line-chart-twoline-improved"
                :options="cryptoChartOptions"
              />
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Orders Row -->
    <v-row>
      <!-- Sell Orders -->
      <v-col cols="12" md="6">
        <v-card elevation="2">
          <v-card-title class="d-flex justify-space-between align-center">
            <span>Sell Order</span>
            <v-menu>
              <template #activator="{ props }">
                <v-btn
                  v-bind="props"
                  icon="mdi-dots-vertical"
                  variant="text"
                  size="small"
                />
              </template>
              <v-list>
                <v-list-item title="This Week" />
                <v-list-item title="This Day" />
              </v-list>
            </v-menu>
          </v-card-title>
          
          <v-card-text>
            <v-select
              v-model="selectedCoin"
              :items="coinOptions"
              label="Select Coin"
              variant="outlined"
              density="compact"
              class="mb-4"
            />
            
            <v-table density="compact">
              <thead>
                <tr>
                  <th class="text-left">Price</th>
                  <th class="text-left">Amount</th>
                  <th class="text-left">Total</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="order in sellOrders" :key="order.id">
                  <td>{{ order.price }}</td>
                  <td>{{ order.amount }}</td>
                  <td>{{ order.total }}</td>
                </tr>
              </tbody>
            </v-table>
            
            <v-btn
              color="primary"
              variant="flat"
              block
              class="mt-4"
              append-icon="mdi-send"
            >
              View All
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>

      <!-- Buy Orders -->
      <v-col cols="12" md="6">
        <v-card
          color="success"
          variant="flat"
          class="text-white"
          elevation="2"
        >
          <v-card-title class="d-flex justify-space-between align-center">
            <span>Buy Order</span>
            <v-menu>
              <template #activator="{ props }">
                <v-btn
                  v-bind="props"
                  icon="mdi-dots-vertical"
                  variant="text"
                  color="white"
                  size="small"
                />
              </template>
              <v-list>
                <v-list-item title="This Week" />
                <v-list-item title="This Day" />
              </v-list>
            </v-menu>
          </v-card-title>
          
          <v-card-text>
            <v-select
              v-model="selectedCoin"
              :items="coinOptions"
              label="Select Coin"
              variant="outlined"
              density="compact"
              class="mb-4"
              color="white"
              bg-color="rgba(255,255,255,0.1)"
            />
            
            <v-table density="compact" class="text-white">
              <thead>
                <tr>
                  <th class="text-left text-white">Price</th>
                  <th class="text-left text-white">Amount</th>
                  <th class="text-left text-white">Total</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="order in buyOrders" :key="order.id" class="text-white">
                  <td>{{ order.price }}</td>
                  <td>{{ order.amount }}</td>
                  <td>{{ order.total }}</td>
                </tr>
              </tbody>
            </v-table>
            
            <v-btn
              color="white"
              variant="flat"
              block
              class="mt-4 text-success"
              append-icon="mdi-send"
            >
              View All
            </v-btn>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- CASL Demo Section -->
    <v-row class="mt-6">
      <v-col cols="12">
        <CASLExample />
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { useHead } from '@unhead/vue'
import { initCandlestick1 } from '@/plugins/charts.js'
import ApexChart from '@/components/shared/ApexChart.vue'
import CASLExample from '@/components/examples/CASLExample.vue'

// Set head tags
useHead({
  title: 'Home - Dashboard',
  meta: [
    {
      name: 'description',
      content: 'Welcome to your cryptocurrency dashboard. View market overview, statistics, and manage your portfolio.'
    }
  ]
})

// Reactive data
const selectedPeriod = ref('month')
const selectedCoin = ref('Dash Coin')

const cryptoOptions = ref([
  { symbol: 'BTC', active: true },
  { symbol: 'XRP', active: false },
  { symbol: 'ETH', active: true },
  { symbol: 'ZEC', active: false },
  { symbol: 'LTC', active: false }
])

const coinOptions = [
  'Dash Coin',
  'Bit Coin',
  'Ethereum',
  'Litecoin'
]

const sellOrders = ref([
  { id: 1, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 2, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 3, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 4, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 5, price: '98.36', amount: '0.25', total: '$147.00' }
])

const buyOrders = ref([
  { id: 1, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 2, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 3, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 4, price: '98.36', amount: '0.25', total: '$147.00' },
  { id: 5, price: '98.36', amount: '0.25', total: '$147.00' }
])

const breadcrumbs = [
  { title: 'Dashboard', disabled: false, href: '/' },
  { title: 'Home', disabled: true }
]

// Chart options
const chartOptions = {
  primary: {
    chart: { background: 'transparent' },
    colors: ['#ffffff'],
    fill: { colors: ['rgba(255,255,255,0.3)'] }
  },
  success: {
    chart: { background: 'transparent' },
    colors: ['#2e7d32'],
    fill: { colors: ['#a5d6a7'] }
  },
  warning: {
    chart: { background: 'transparent' },
    colors: ['#f57f17'],
    fill: { colors: ['#fff176'] }
  },
  error: {
    chart: { background: '#ffebee' },
    colors: ['#c62828'],
    fill: { colors: ['#ef9a9a'] }
  }
}

const cryptoChartOptions = {
  chart: {
    height: 207,
    toolbar: { show: false }
  },
  legend: { show: false },
  yaxis: { show: false }
}

// Methods
const toggleCrypto = (symbol) => {
  const crypto = cryptoOptions.value.find(c => c.symbol === symbol)
  if (crypto) {
    crypto.active = !crypto.active
  }
}

// Lifecycle
onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      const el = document.querySelector('#candlestick-1-vuetify')
      if (el) {
        initCandlestick1()
      }
    }, 100)
  })
})
</script>

<style scoped>
.stats-card {
  height: 100%;
  transition: transform 0.2s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.chart-container {
  height: 60px;
  overflow: hidden;
}

.legend-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.bg-purple {
  background-color: #9c27b0;
}

/* Table styling for dark cards */
.v-table--density-compact > .v-table__wrapper > table > tbody > tr > td {
  height: 36px;
}

/* Custom scrollbar */
.chart-container::-webkit-scrollbar {
  display: none;
}
</style>
