<template>
  <div class="admin-view">
    <!-- <PERSON> Header -->
    <v-row class="mb-6">
      <v-col cols="12">
        <div class="d-flex justify-space-between align-center flex-wrap">
          <div>
            <h1 class="text-h4 font-weight-bold mb-2">Admin Panel</h1>
            <v-breadcrumbs
              :items="breadcrumbs"
              density="compact"
              class="pa-0"
            >
              <template #divider>
                <v-icon>mdi-chevron-right</v-icon>
              </template>
            </v-breadcrumbs>
          </div>
          <v-btn
            color="primary"
            prepend-icon="mdi-plus"
            variant="flat"
            @click="showCreateDialog = true"
          >
            Add User
          </v-btn>
        </div>
      </v-col>
    </v-row>

    <!-- Admin Stats Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card color="primary" variant="flat" class="text-white">
          <v-card-text>
            <div class="d-flex justify-space-between align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.totalUsers }}</div>
                <div class="text-body-2">Total Users</div>
              </div>
              <v-icon size="40">mdi-account-group</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="success" variant="flat" class="text-white">
          <v-card-text>
            <div class="d-flex justify-space-between align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.activeUsers }}</div>
                <div class="text-body-2">Active Users</div>
              </div>
              <v-icon size="40">mdi-account-check</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="warning" variant="flat" class="text-white">
          <v-card-text>
            <div class="d-flex justify-space-between align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.pendingRequests }}</div>
                <div class="text-body-2">Pending Requests</div>
              </div>
              <v-icon size="40">mdi-clock-alert</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card color="error" variant="flat" class="text-white">
          <v-card-text>
            <div class="d-flex justify-space-between align-center">
              <div>
                <div class="text-h4 font-weight-bold">{{ stats.systemAlerts }}</div>
                <div class="text-body-2">System Alerts</div>
              </div>
              <v-icon size="40">mdi-alert</v-icon>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- User Management Section -->
    <v-row>
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title class="d-flex justify-space-between align-center">
            <span>User Management</span>
            <div class="d-flex gap-2">
              <v-text-field
                v-model="search"
                prepend-inner-icon="mdi-magnify"
                label="Search users..."
                variant="outlined"
                density="compact"
                hide-details
                clearable
                style="max-width: 300px;"
              />
              <v-btn
                icon="mdi-refresh"
                variant="text"
                @click="refreshUsers"
                :loading="loading"
              />
            </div>
          </v-card-title>

          <v-data-table
            :headers="headers"
            :items="filteredUsers"
            :search="search"
            :loading="loading"
            class="elevation-0"
            item-value="id"
          >
            <!-- Avatar column -->
            <template #item.avatar="{ item }">
              <v-avatar size="32">
                <v-img :src="item.avatar" :alt="item.name" />
              </v-avatar>
            </template>

            <!-- Role column -->
            <template #item.role="{ item }">
              <v-chip
                :color="getRoleColor(item.role)"
                size="small"
                variant="flat"
              >
                {{ item.role }}
              </v-chip>
            </template>

            <!-- Status column -->
            <template #item.status="{ item }">
              <v-chip
                :color="item.status === 'active' ? 'success' : 'error'"
                size="small"
                variant="flat"
              >
                {{ item.status }}
              </v-chip>
            </template>

            <!-- Last Login column -->
            <template #item.lastLogin="{ item }">
              <span class="text-body-2">{{ formatDate(item.lastLogin) }}</span>
            </template>

            <!-- Actions column -->
            <template #item.actions="{ item }">
              <div class="d-flex gap-1">
                <v-btn
                  icon="mdi-pencil"
                  size="small"
                  variant="text"
                  @click="editUser(item)"
                />
                <v-btn
                  icon="mdi-delete"
                  size="small"
                  variant="text"
                  color="error"
                  @click="deleteUser(item)"
                />
                <v-menu>
                  <template #activator="{ props }">
                    <v-btn
                      v-bind="props"
                      icon="mdi-dots-vertical"
                      size="small"
                      variant="text"
                    />
                  </template>
                  <v-list density="compact">
                    <v-list-item @click="viewUserDetails(item)">
                      <v-list-item-title>View Details</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="resetPassword(item)">
                      <v-list-item-title>Reset Password</v-list-item-title>
                    </v-list-item>
                    <v-list-item @click="toggleUserStatus(item)">
                      <v-list-item-title>
                        {{ item.status === 'active' ? 'Deactivate' : 'Activate' }}
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </div>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>

    <!-- System Logs Section -->
    <v-row class="mt-6">
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title>Recent System Logs</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item
                v-for="log in systemLogs"
                :key="log.id"
                class="mb-2"
              >
                <template #prepend>
                  <v-icon :color="getLogColor(log.type)">
                    {{ getLogIcon(log.type) }}
                  </v-icon>
                </template>
                <v-list-item-title>{{ log.message }}</v-list-item-title>
                <v-list-item-subtitle>
                  {{ formatDate(log.timestamp) }} - {{ log.user }}
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Create User Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="600">
      <v-card>
        <v-card-title>Add New User</v-card-title>
        <v-card-text>
          <v-form ref="createForm" v-model="createFormValid">
            <v-text-field
              v-model="newUser.name"
              label="Full Name"
              variant="outlined"
              :rules="[v => !!v || 'Name is required']"
              class="mb-4"
            />
            <v-text-field
              v-model="newUser.email"
              label="Email"
              type="email"
              variant="outlined"
              :rules="[v => !!v || 'Email is required', v => /.+@.+\..+/.test(v) || 'Email must be valid']"
              class="mb-4"
            />
            <v-select
              v-model="newUser.role"
              :items="roleOptions"
              label="Role"
              variant="outlined"
              :rules="[v => !!v || 'Role is required']"
              class="mb-4"
            />
            <v-select
              v-model="newUser.status"
              :items="statusOptions"
              label="Status"
              variant="outlined"
              :rules="[v => !!v || 'Status is required']"
            />
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer />
          <v-btn @click="showCreateDialog = false">Cancel</v-btn>
          <v-btn
            color="primary"
            :disabled="!createFormValid"
            @click="createUser"
          >
            Create User
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Snackbar for notifications -->
    <v-snackbar
      v-model="showSnackbar"
      :color="snackbarColor"
      timeout="3000"
    >
      {{ snackbarMessage }}
      <template #actions>
        <v-btn variant="text" @click="showSnackbar = false">
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useHead } from '@unhead/vue'

// Set head tags
useHead({
  title: 'Admin Panel - Dashboard',
  meta: [
    {
      name: 'description',
      content: 'Administrative panel for user management and system monitoring'
    }
  ]
})

// Reactive data
const loading = ref(false)
const search = ref('')
const showCreateDialog = ref(false)
const createFormValid = ref(false)
const showSnackbar = ref(false)
const snackbarMessage = ref('')
const snackbarColor = ref('success')

// Stats data
const stats = ref({
  totalUsers: 1247,
  activeUsers: 1156,
  pendingRequests: 23,
  systemAlerts: 5
})

// Table headers
const headers = [
  { title: 'Avatar', key: 'avatar', sortable: false },
  { title: 'Name', key: 'name' },
  { title: 'Email', key: 'email' },
  { title: 'Role', key: 'role' },
  { title: 'Status', key: 'status' },
  { title: 'Last Login', key: 'lastLogin' },
  { title: 'Actions', key: 'actions', sortable: false }
]

// Users data
const users = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastLogin: new Date('2024-01-15'),
    avatar: '/images/avatar/user-1.png'
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'moderator',
    status: 'active',
    lastLogin: new Date('2024-01-14'),
    avatar: '/images/avatar/user-1.png'
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    lastLogin: new Date('2024-01-10'),
    avatar: '/images/avatar/user-1.png'
  }
])

// System logs
const systemLogs = ref([
  {
    id: 1,
    type: 'info',
    message: 'User login successful',
    user: '<EMAIL>',
    timestamp: new Date()
  },
  {
    id: 2,
    type: 'warning',
    message: 'Failed login attempt',
    user: '<EMAIL>',
    timestamp: new Date(Date.now() - 300000)
  },
  {
    id: 3,
    type: 'error',
    message: 'Database connection timeout',
    user: 'system',
    timestamp: new Date(Date.now() - 600000)
  }
])

// Form data
const newUser = ref({
  name: '',
  email: '',
  role: '',
  status: 'active'
})

const roleOptions = ['admin', 'moderator', 'user']
const statusOptions = ['active', 'inactive']

const breadcrumbs = [
  { title: 'Dashboard', disabled: false, href: '/' },
  { title: 'Admin Panel', disabled: true }
]

// Computed
const filteredUsers = computed(() => {
  if (!search.value) return users.value
  return users.value.filter(user =>
    user.name.toLowerCase().includes(search.value.toLowerCase()) ||
    user.email.toLowerCase().includes(search.value.toLowerCase())
  )
})

// Methods
const getRoleColor = (role) => {
  switch (role) {
    case 'admin': return 'error'
    case 'moderator': return 'warning'
    case 'user': return 'success'
    default: return 'grey'
  }
}

const getLogColor = (type) => {
  switch (type) {
    case 'error': return 'error'
    case 'warning': return 'warning'
    case 'info': return 'info'
    default: return 'grey'
  }
}

const getLogIcon = (type) => {
  switch (type) {
    case 'error': return 'mdi-alert-circle'
    case 'warning': return 'mdi-alert'
    case 'info': return 'mdi-information'
    default: return 'mdi-circle'
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(date))
}

const refreshUsers = () => {
  loading.value = true
  setTimeout(() => {
    loading.value = false
    showNotification('Users refreshed successfully', 'success')
  }, 1000)
}

const editUser = (user) => {
  console.log('Edit user:', user)
  showNotification(`Editing ${user.name}`, 'info')
}

const deleteUser = (user) => {
  console.log('Delete user:', user)
  showNotification(`${user.name} deleted`, 'error')
}

const viewUserDetails = (user) => {
  console.log('View user details:', user)
  showNotification(`Viewing details for ${user.name}`, 'info')
}

const resetPassword = (user) => {
  console.log('Reset password for:', user)
  showNotification(`Password reset for ${user.name}`, 'warning')
}

const toggleUserStatus = (user) => {
  user.status = user.status === 'active' ? 'inactive' : 'active'
  showNotification(`${user.name} ${user.status}`, 'success')
}

const createUser = () => {
  console.log('Create user:', newUser.value)
  users.value.push({
    id: users.value.length + 1,
    ...newUser.value,
    lastLogin: new Date(),
    avatar: '/images/avatar/user-1.png'
  })
  
  showCreateDialog.value = false
  newUser.value = { name: '', email: '', role: '', status: 'active' }
  showNotification('User created successfully', 'success')
}

const showNotification = (message, color = 'success') => {
  snackbarMessage.value = message
  snackbarColor.value = color
  showSnackbar.value = true
}

// Lifecycle
onMounted(() => {
  // Initialize component
})
</script>

<style scoped>
.admin-view {
  padding: 0;
}

.v-card {
  transition: transform 0.2s ease;
}

.v-card:hover {
  transform: translateY(-1px);
}

.v-data-table {
  background: transparent;
}

.v-list-item {
  border-radius: 8px;
  margin-bottom: 4px;
}

.v-list-item:hover {
  background-color: rgba(var(--v-theme-primary), 0.04);
}
</style>
