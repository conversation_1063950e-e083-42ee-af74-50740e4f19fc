<script setup>
import { ref, onMounted } from 'vue'

// Current layout state
const currentLayout = ref('default')

// Layout options with their corresponding CSS classes
const layoutOptions = [
  {
    value: 'default',
    label: 'Default Layout',
    description: 'Standard full sidebar with complete navigation menu visible.',
    classes: [],
    themeSettingSelector: '.menu-style .menu-click'
  },
  {
    value: 'boxed',
    label: 'Boxed Layout',
    description: 'Constrained width layout with centered content (max-width: 1440px).',
    classes: ['layout-width-boxed'],
    themeSettingSelector: '.layout-width .boxed'
  },
  {
    value: 'icon-default',
    label: 'Icon Default Layout',
    description: 'Collapsed sidebar (75px) showing only icons - STAYS collapsed permanently (no hover expansion).',
    classes: ['menu-style-icon-default'],
    themeSettingSelector: '.menu-style .icon-default'
  },
  {
    value: 'icon-hover',
    label: 'Icon Hover Layout',
    description: 'Collapsed sidebar (75px) that EXPANDS to full width (256px) on hover.',
    classes: ['menu-style-icon'],
    themeSettingSelector: '.menu-style .icon-hover'
  }
]

// Apply layout classes to the existing layout-wrap element
const applyLayoutClasses = (classes) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    // Remove all layout-specific classes
    layoutWrap.classList.remove('layout-width-boxed', 'menu-style-icon', 'menu-style-icon-default')

    // Add the new classes
    classes.forEach(className => {
      layoutWrap.classList.add(className)
    })
  }
}

// Switch layout using theme settings approach
const switchLayout = (layoutValue) => {
  currentLayout.value = layoutValue
  const option = layoutOptions.find(opt => opt.value === layoutValue)
  if (option) {
    if (layoutValue === 'icon-default') {
      // For icon-default, redirect to a page that uses the icon-default layout
      console.log('Switching to icon-default layout - this would use the IconDefault.vue component')
      // Apply the class for demonstration
      applyLayoutClasses(option.classes)
    } else {
      // Use jQuery to trigger the theme setting buttons if available
      if (window.$ && option.themeSettingSelector) {
        try {
          window.$(option.themeSettingSelector).trigger('click')
        } catch (e) {
          // Fallback to direct class manipulation
          applyLayoutClasses(option.classes)
        }
      } else {
        // Fallback to direct class manipulation
        applyLayoutClasses(option.classes)
      }
    }
  }
}

// Get current layout option
const getCurrentLayoutOption = () => {
  return layoutOptions.find(opt => opt.value === currentLayout.value) || layoutOptions[0]
}

// Debug current state
const debugCurrentState = () => {
  console.log('=== LAYOUT DEBUG ===')
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    console.log('Layout wrap element:', layoutWrap)
    console.log('Layout wrap classes:', layoutWrap.className)
    console.log('Has layout-width-boxed:', layoutWrap.classList.contains('layout-width-boxed'))
    console.log('Has menu-style-icon:', layoutWrap.classList.contains('menu-style-icon'))
    console.log('Has menu-style-icon-default:', layoutWrap.classList.contains('menu-style-icon-default'))
  } else {
    console.log('Layout wrap element not found!')
  }

  // Check if theme settings elements exist
  console.log('Theme settings elements:')
  console.log('- .menu-style .icon-hover:', document.querySelector('.menu-style .icon-hover'))
  console.log('- .menu-style .icon-default:', document.querySelector('.menu-style .icon-default'))
  console.log('- .menu-style .menu-click:', document.querySelector('.menu-style .menu-click'))
  console.log('- .layout-width .boxed:', document.querySelector('.layout-width .boxed'))
  console.log('- .layout-width .full:', document.querySelector('.layout-width .full'))

  console.log('Current layout state:', currentLayout.value)
  console.log('==================')
}

// Detect current layout from DOM
const detectCurrentLayout = () => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (layoutWrap.classList.contains('layout-width-boxed')) {
      currentLayout.value = 'boxed'
    } else if (layoutWrap.classList.contains('menu-style-icon-default')) {
      currentLayout.value = 'icon-default'
    } else if (layoutWrap.classList.contains('menu-style-icon')) {
      currentLayout.value = 'icon-hover'
    } else {
      currentLayout.value = 'default'
    }
  }
}

onMounted(() => {
  // Detect current layout on mount
  setTimeout(detectCurrentLayout, 100)

  // Add debugging
  console.log('LayoutTestView mounted')
  setTimeout(() => {
    const layoutWrap = document.querySelector('.layout-wrap')
    if (layoutWrap) {
      console.log('Layout wrap found:', layoutWrap)
      console.log('Layout wrap classes:', layoutWrap.className)
    } else {
      console.log('Layout wrap not found')
    }
  }, 500)
})
</script>

<template>
  <div class="layout-test-container">
    <!-- Layout Switcher Controls -->
    <div class="layout-controls">
      <h2>Layout Test Page</h2>
      <p>Use the buttons below to test different layout implementations:</p>

      <div class="layout-buttons">
        <button
          v-for="option in layoutOptions"
          :key="option.value"
          @click="switchLayout(option.value)"
          :class="['btn', currentLayout === option.value ? 'btn-primary' : 'btn-outline-primary']"
        >
          {{ option.label }}
        </button>
      </div>

      <div class="current-layout-info">
        <strong>Current Layout:</strong> {{ getCurrentLayoutOption().label }}
        <br>
        <strong>Description:</strong> {{ getCurrentLayoutOption().description }}
        <br>
        <strong>CSS Classes:</strong>
        <code v-if="getCurrentLayoutOption().classes.length > 0">
          {{ getCurrentLayoutOption().classes.join(', ') }}
        </code>
        <span v-else>None (default)</span>
      </div>

      <!-- Debug Section -->
      <div class="debug-info">
        <h4>Debug Information</h4>
        <p><strong>Instructions:</strong></p>
        <ol>
          <li>Open browser console (F12) to see debug messages</li>
          <li>Click the gear icon in the header to open theme settings</li>
          <li>Try changing "Menu Style" and "Layout Width" options</li>
          <li>Watch the console for debug messages and DOM changes</li>
        </ol>
        <button @click="debugCurrentState" class="btn btn-info">Debug Current State</button>
      </div>
    </div>

    <!-- Layout Preview -->
    <div class="layout-preview">
      <h3>Layout Preview</h3>
      <p>This demonstrates how each layout affects the sidebar and content area:</p>

      <div class="layout-descriptions">
        <div
          v-for="option in layoutOptions"
          :key="option.value"
          class="layout-description"
          :class="{ 'active': currentLayout === option.value }"
        >
          <h4>{{ option.label }}</h4>
          <p>{{ option.description }}</p>
          <div class="css-classes">
            <strong>CSS Classes:</strong>
            <code v-if="option.classes.length > 0">
              {{ option.classes.join(', ') }}
            </code>
            <span v-else>None (default)</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Theme Settings Integration -->
    <div class="theme-integration">
      <h3>Theme Settings Integration</h3>
      <p>These layouts work with the theme settings panel by applying CSS classes to the <code>.layout-wrap</code> element:</p>

      <div class="integration-details">
        <h4>How it works:</h4>
        <ol>
          <li>The theme settings in <code>theme-settings.js</code> use jQuery to apply CSS classes</li>
          <li>These classes are applied to the <code>.layout-wrap</code> element in the DOM</li>
          <li>The CSS in <code>_theme-settings.scss</code> responds to these classes</li>
          <li>The layout changes automatically based on the applied classes</li>
        </ol>
      </div>

      <div class="css-classes-info">
        <h4>CSS Classes Used:</h4>
        <ul>
          <li><code>layout-wrap</code> - Base layout wrapper (always present)</li>
          <li><code>layout-width-boxed</code> - Applied by "Layout Width > Boxed" setting</li>
          <li><code>menu-style-icon-default</code> - Applied by "Menu Style > Icon Default" setting</li>
          <li><code>menu-style-icon</code> - Applied by "Menu Style > Icon Hover" setting</li>
        </ul>
      </div>

      <div class="instructions">
        <h4>Try it out:</h4>
        <p>1. Use the buttons above to switch layouts programmatically</p>
        <p>2. Use the theme settings panel (gear icon in header) to see the same effects</p>
        <p>3. Notice how the sidebar behavior changes with each layout</p>
        <p>4. <router-link to="/icon-default-demo" class="text-primary">Visit Icon Default Demo Page</router-link> - Uses the exact HTML structure</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.layout-test-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.layout-controls {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.layout-buttons {
  display: flex;
  gap: 1rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: white;
  color: #007bff;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn:hover {
  background: #0056b3;
  color: white;
}

.current-layout-info {
  margin-top: 1rem;
  padding: 0.5rem;
  background: #e9ecef;
  border-radius: 4px;
}

.layout-preview {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
}

.layout-descriptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.layout-description {
  padding: 1rem;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.layout-description.active {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.layout-description h4 {
  margin-top: 0;
  color: #495057;
}

.css-classes {
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.css-classes code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.theme-integration {
  background: #fff3cd;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #ffeaa7;
}

.integration-details,
.css-classes-info,
.instructions {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 4px;
}

.theme-integration ul,
.theme-integration ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.theme-integration code {
  background: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  border: 1px solid #dee2e6;
}

.instructions p {
  margin: 0.5rem 0;
}

.debug-info {
  background: #e3f2fd;
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid #2196f3;
  margin-top: 2rem;
}

.debug-info h4 {
  margin-top: 0;
  color: #1976d2;
}

.debug-info ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.btn-info {
  background-color: #2196f3;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.btn-info:hover {
  background-color: #1976d2;
}
</style>
