<template>
  <div class="user-view">
    <div class="container-fluid">
      <!-- <PERSON> Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">User Dashboard</h1>
              <p class="text-muted">Welcome back, {{ userName }}!</p>
            </div>
            <div class="badge bg-primary">{{ userRole }}</div>
          </div>
        </div>
      </div>

      <!-- User Profile Card -->
      <div class="row mb-4">
        <div class="col-md-4">
          <div class="card">
            <div class="card-body text-center">
              <img 
                :src="user?.avatar || '/images/avatar/default.png'" 
                :alt="userName"
                class="rounded-circle mb-3"
                width="80"
                height="80"
              />
              <h5 class="card-title">{{ userName }}</h5>
              <p class="text-muted">{{ user?.email }}</p>
              <span class="badge bg-primary">{{ userRole }}</span>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-md-8">
          <div class="row">
            <div class="col-md-4">
              <div class="card text-center">
                <div class="card-body">
                  <i class="icon-file-text display-6 text-primary mb-2"></i>
                  <h6 class="card-title">My Posts</h6>
                  <h4 class="text-primary">12</h4>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card text-center">
                <div class="card-body">
                  <i class="icon-message-circle display-6 text-success mb-2"></i>
                  <h6 class="card-title">Comments</h6>
                  <h4 class="text-success">45</h4>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card text-center">
                <div class="card-body">
                  <i class="icon-heart display-6 text-danger mb-2"></i>
                  <h6 class="card-title">Likes</h6>
                  <h4 class="text-danger">128</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- User Actions -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Available Actions</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Actions based on permissions -->
                <CanAccess action="create" subject="Post" class="col-md-3 mb-3">
                  <button class="btn btn-primary w-100">
                    <i class="icon-plus me-2"></i>
                    Create Post
                  </button>
                </CanAccess>

                <CanAccess action="read" subject="Settings" class="col-md-3 mb-3">
                  <button class="btn btn-secondary w-100">
                    <i class="icon-settings me-2"></i>
                    Settings
                  </button>
                </CanAccess>

                <CanAccess action="read" subject="AdminPanel" class="col-md-3 mb-3">
                  <button class="btn btn-warning w-100" @click="goToAdmin">
                    <i class="icon-shield me-2"></i>
                    Admin Panel
                  </button>
                </CanAccess>

                <div class="col-md-3 mb-3">
                  <button class="btn btn-info w-100">
                    <i class="icon-user me-2"></i>
                    Edit Profile
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- My Posts -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
              <h5 class="card-title mb-0">My Posts</h5>
              <CanAccess action="create" subject="Post">
                <button class="btn btn-primary btn-sm">
                  <i class="icon-plus me-1"></i>
                  New Post
                </button>
              </CanAccess>
            </div>
            <div class="card-body">
              <div class="row">
                <div v-for="post in mockPosts" :key="post.id" class="col-md-6 mb-3">
                  <div class="card">
                    <div class="card-body">
                      <h6 class="card-title">{{ post.title }}</h6>
                      <p class="card-text text-muted">{{ post.excerpt }}</p>
                      <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">{{ formatDate(post.createdAt) }}</small>
                        <div class="btn-group btn-group-sm">
                          <CanAccess action="update" subject="Post" :field="{ authorId: user?.id }">
                            <button class="btn btn-outline-primary">
                              <i class="icon-edit"></i>
                            </button>
                          </CanAccess>
                          <CanAccess action="delete" subject="Post" :field="{ authorId: user?.id }">
                            <button class="btn btn-outline-danger">
                              <i class="icon-trash"></i>
                            </button>
                          </CanAccess>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import CanAccess from '@/components/auth/CanAccess.vue'

const router = useRouter()
const { user, userName, userRole } = useAuth()

// Mock posts data
const mockPosts = ref([
  {
    id: 1,
    title: 'Getting Started with Vue 3',
    excerpt: 'Learn the basics of Vue 3 and its new features...',
    createdAt: new Date('2024-01-20'),
    authorId: user.value?.id
  },
  {
    id: 2,
    title: 'Understanding CASL Permissions',
    excerpt: 'A comprehensive guide to implementing authorization...',
    createdAt: new Date('2024-01-18'),
    authorId: user.value?.id
  },
  {
    id: 3,
    title: 'Building Responsive Layouts',
    excerpt: 'Tips and tricks for creating mobile-friendly designs...',
    createdAt: new Date('2024-01-15'),
    authorId: user.value?.id
  },
  {
    id: 4,
    title: 'State Management with Pinia',
    excerpt: 'Modern state management for Vue applications...',
    createdAt: new Date('2024-01-12'),
    authorId: user.value?.id
  }
])

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date)
}

const goToAdmin = () => {
  router.push('/admin')
}
</script>

<style scoped>
.user-view {
  padding: 2rem 0;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.display-6 {
  font-size: 1.75rem;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.badge {
  font-size: 0.75rem;
  text-transform: capitalize;
}
</style>
