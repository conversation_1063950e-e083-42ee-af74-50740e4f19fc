<template>
  <div class="admin-view">
    <div class="container-fluid">
      <!-- <PERSON> Header -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h1 class="h3 mb-0">Admin Dashboard</h1>
              <p class="text-muted">Manage users, content, and system settings</p>
            </div>
            <div class="badge bg-danger">Admin Only</div>
          </div>
        </div>
      </div>

      <!-- Admin Stats -->
      <div class="row mb-4">
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="icon-users display-4 text-primary mb-2"></i>
              <h5 class="card-title">Total Users</h5>
              <h3 class="text-primary">1,234</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="icon-file-text display-4 text-success mb-2"></i>
              <h5 class="card-title">Total Posts</h5>
              <h3 class="text-success">5,678</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="icon-message-circle display-4 text-info mb-2"></i>
              <h5 class="card-title">Comments</h5>
              <h3 class="text-info">12,345</h3>
            </div>
          </div>
        </div>
        <div class="col-md-3">
          <div class="card text-center">
            <div class="card-body">
              <i class="icon-shield display-4 text-warning mb-2"></i>
              <h5 class="card-title">Security Events</h5>
              <h3 class="text-warning">23</h3>
            </div>
          </div>
        </div>
      </div>

      <!-- Admin Actions -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Admin Actions</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-4 mb-3">
                  <button class="btn btn-primary w-100">
                    <i class="icon-user-plus me-2"></i>
                    Create User
                  </button>
                </div>
                <div class="col-md-4 mb-3">
                  <button class="btn btn-success w-100">
                    <i class="icon-settings me-2"></i>
                    System Settings
                  </button>
                </div>
                <div class="col-md-4 mb-3">
                  <button class="btn btn-warning w-100">
                    <i class="icon-database me-2"></i>
                    Backup Data
                  </button>
                </div>
                <div class="col-md-4 mb-3">
                  <button class="btn btn-info w-100">
                    <i class="icon-bar-chart me-2"></i>
                    View Reports
                  </button>
                </div>
                <div class="col-md-4 mb-3">
                  <button class="btn btn-secondary w-100">
                    <i class="icon-mail me-2"></i>
                    Send Notifications
                  </button>
                </div>
                <div class="col-md-4 mb-3">
                  <button class="btn btn-danger w-100">
                    <i class="icon-trash-2 me-2"></i>
                    Cleanup Data
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- User Management Table -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">User Management</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Email</th>
                      <th>Role</th>
                      <th>Status</th>
                      <th>Last Login</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="user in mockUsers" :key="user.id">
                      <td>{{ user.id }}</td>
                      <td>
                        <div class="d-flex align-items-center">
                          <img 
                            :src="user.avatar || '/images/avatar/default.png'" 
                            :alt="user.name"
                            class="rounded-circle me-2"
                            width="32"
                            height="32"
                          />
                          {{ user.name }}
                        </div>
                      </td>
                      <td>{{ user.email }}</td>
                      <td>
                        <span class="badge" :class="getRoleBadgeClass(user.role)">
                          {{ user.role }}
                        </span>
                      </td>
                      <td>
                        <span class="badge bg-success">Active</span>
                      </td>
                      <td>{{ formatDate(new Date()) }}</td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button class="btn btn-outline-primary">
                            <i class="icon-edit"></i>
                          </button>
                          <button class="btn btn-outline-danger">
                            <i class="icon-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useAuthStore } from '@/stores/auth'

const { user } = useAuth()
const authStore = useAuthStore()

const mockUsers = computed(() => authStore.mockUsers)

const getRoleBadgeClass = (role) => {
  const classes = {
    admin: 'bg-danger',
    moderator: 'bg-warning',
    user: 'bg-primary'
  }
  return classes[role] || 'bg-secondary'
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}
</script>

<style scoped>
.admin-view {
  padding: 2rem 0;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.display-4 {
  font-size: 2.5rem;
}

.btn-group-sm .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.table th {
  border-top: none;
  font-weight: 600;
  color: #495057;
}

.badge {
  font-size: 0.75rem;
}
</style>
