<script setup>
import { useHead } from '@unhead/vue'

// Simple test of useHead functionality
useHead({
  title: 'Test Head Page',
  meta: [
    {
      name: 'description',
      content: 'This is a test page to verify useHead functionality'
    },
    {
      name: 'keywords',
      content: 'test, head, vue, unhead'
    }
  ]
})
</script>

<template>
  <div class="main-content-inner">
    <div class="main-content-wrap">
      <div class="tf-container">
        <div class="row">
          <div class="col-12">
            <div class="wg-box">
              <h1>Test Head Page</h1>
              <p>This page tests the useHead functionality.</p>
              <p>Check the browser's document title and view source to see the meta tags.</p>
              
              <div class="alert alert-info">
                <h4>Expected Results:</h4>
                <ul>
                  <li>Page title should be: "Test Head Page | Dashboard App"</li>
                  <li>Meta description should be set</li>
                  <li>Meta keywords should be set</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.alert {
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}
</style>
