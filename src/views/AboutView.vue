<script setup>
import { useHead } from '@unhead/vue'

// Set page-specific head tags
useHead({
  title: 'About Us',
  meta: [
    {
      name: 'description',
      content: 'Learn more about our company, mission, and the team behind this amazing dashboard application.'
    },
    {
      name: 'keywords',
      content: 'about, company, team, mission, dashboard, vue.js'
    },
    {
      property: 'og:title',
      content: 'About Us - Dashboard App'
    },
    {
      property: 'og:description',
      content: 'Learn more about our company and the team behind this dashboard application.'
    }
  ]
})
</script>

<template>
  <div class="about">
    <h1>This is an about page</h1>
    <p>This page demonstrates how to use the useHead composable to set page-specific meta tags.</p>
  </div>
</template>

<style>
@media (min-width: 1024px) {
  .about {
    min-height: 100vh;
    display: flex;
    align-items: center;
  }
}
</style>
