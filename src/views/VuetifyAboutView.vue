<template>
  <div class="about-view">
    <!-- Hero Section -->
    <v-row class="mb-8">
      <v-col cols="12">
        <v-card
          color="primary"
          variant="flat"
          class="text-white hero-card"
          elevation="4"
        >
          <v-card-text class="pa-8">
            <v-row align="center">
              <v-col cols="12" md="8">
                <h1 class="text-h3 font-weight-bold mb-4">About Our Platform</h1>
                <p class="text-h6 font-weight-light mb-6 opacity-90">
                  A modern, secure, and feature-rich dashboard built with Vue 3, Vuetify 3, 
                  and advanced role-based access control.
                </p>
                <v-btn
                  color="white"
                  variant="flat"
                  size="large"
                  prepend-icon="mdi-rocket-launch"
                  class="text-primary"
                >
                  Get Started
                </v-btn>
              </v-col>
              <v-col cols="12" md="4" class="text-center">
                <v-img
                  src="/images/illustrations/about-hero.svg"
                  max-width="300"
                  class="mx-auto"
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Features Section -->
    <v-row class="mb-8">
      <v-col cols="12">
        <div class="text-center mb-8">
          <h2 class="text-h4 font-weight-bold mb-4">Platform Features</h2>
          <p class="text-h6 font-weight-light text-grey">
            Discover what makes our platform unique and powerful
          </p>
        </div>
      </v-col>

      <v-col cols="12" sm="6" lg="4">
        <v-card class="feature-card h-100" elevation="2">
          <v-card-text class="text-center pa-6">
            <v-avatar size="80" color="primary" class="mb-4">
              <v-icon size="40" color="white">mdi-vuetify</v-icon>
            </v-avatar>
            <h3 class="text-h5 font-weight-bold mb-3">Vuetify 3</h3>
            <p class="text-body-1 text-grey">
              Built with the latest Vuetify 3 framework, providing beautiful 
              Material Design components and responsive layouts.
            </p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="4">
        <v-card class="feature-card h-100" elevation="2">
          <v-card-text class="text-center pa-6">
            <v-avatar size="80" color="success" class="mb-4">
              <v-icon size="40" color="white">mdi-shield-check</v-icon>
            </v-avatar>
            <h3 class="text-h5 font-weight-bold mb-3">CASL Security</h3>
            <p class="text-body-1 text-grey">
              Advanced role-based access control with CASL, ensuring secure 
              and granular permission management.
            </p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="4">
        <v-card class="feature-card h-100" elevation="2">
          <v-card-text class="text-center pa-6">
            <v-avatar size="80" color="warning" class="mb-4">
              <v-icon size="40" color="white">mdi-palette</v-icon>
            </v-avatar>
            <h3 class="text-h5 font-weight-bold mb-3">Theme System</h3>
            <p class="text-body-1 text-grey">
              Comprehensive theming system with dark/light modes, custom colors, 
              and layout options for personalized experience.
            </p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="4">
        <v-card class="feature-card h-100" elevation="2">
          <v-card-text class="text-center pa-6">
            <v-avatar size="80" color="info" class="mb-4">
              <v-icon size="40" color="white">mdi-chart-line</v-icon>
            </v-avatar>
            <h3 class="text-h5 font-weight-bold mb-3">Analytics</h3>
            <p class="text-body-1 text-grey">
              Real-time analytics and reporting with interactive charts 
              and comprehensive data visualization.
            </p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="4">
        <v-card class="feature-card h-100" elevation="2">
          <v-card-text class="text-center pa-6">
            <v-avatar size="80" color="error" class="mb-4">
              <v-icon size="40" color="white">mdi-responsive</v-icon>
            </v-avatar>
            <h3 class="text-h5 font-weight-bold mb-3">Responsive</h3>
            <p class="text-body-1 text-grey">
              Fully responsive design that works perfectly on desktop, 
              tablet, and mobile devices.
            </p>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="4">
        <v-card class="feature-card h-100" elevation="2">
          <v-card-text class="text-center pa-6">
            <v-avatar size="80" color="purple" class="mb-4">
              <v-icon size="40" color="white">mdi-cog</v-icon>
            </v-avatar>
            <h3 class="text-h5 font-weight-bold mb-3">Configurable</h3>
            <p class="text-body-1 text-grey">
              Highly configurable with extensive customization options 
              for layouts, themes, and user preferences.
            </p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Technology Stack -->
    <v-row class="mb-8">
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title class="text-h5 font-weight-bold pa-6">
            Technology Stack
          </v-card-title>
          <v-card-text class="pa-6">
            <v-row>
              <v-col cols="12" md="6">
                <h4 class="text-h6 font-weight-medium mb-4">Frontend</h4>
                <v-list density="compact">
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="success">mdi-vuejs</v-icon>
                    </template>
                    <v-list-item-title>Vue 3 with Composition API</v-list-item-title>
                  </v-list-item>
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="primary">mdi-vuetify</v-icon>
                    </template>
                    <v-list-item-title>Vuetify 3 Material Design</v-list-item-title>
                  </v-list-item>
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="warning">mdi-router</v-icon>
                    </template>
                    <v-list-item-title>Vue Router 4</v-list-item-title>
                  </v-list-item>
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="info">mdi-database</v-icon>
                    </template>
                    <v-list-item-title>Pinia State Management</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="6">
                <h4 class="text-h6 font-weight-medium mb-4">Features</h4>
                <v-list density="compact">
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="error">mdi-shield-check</v-icon>
                    </template>
                    <v-list-item-title>CASL Authorization</v-list-item-title>
                  </v-list-item>
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="purple">mdi-chart-areaspline</v-icon>
                    </template>
                    <v-list-item-title>ApexCharts Integration</v-list-item-title>
                  </v-list-item>
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="teal">mdi-test-tube</v-icon>
                    </template>
                    <v-list-item-title>Vitest Testing Framework</v-list-item-title>
                  </v-list-item>
                  <v-list-item>
                    <template #prepend>
                      <v-icon color="orange">mdi-webpack</v-icon>
                    </template>
                    <v-list-item-title>Vite Build Tool</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Statistics -->
    <v-row class="mb-8">
      <v-col cols="12">
        <v-card
          color="gradient"
          variant="flat"
          class="stats-card"
          elevation="4"
        >
          <v-card-text class="pa-8">
            <div class="text-center mb-6">
              <h3 class="text-h4 font-weight-bold text-white mb-2">Platform Statistics</h3>
              <p class="text-h6 font-weight-light text-white opacity-90">
                Numbers that showcase our platform's capabilities
              </p>
            </div>
            
            <v-row>
              <v-col cols="6" md="3" class="text-center">
                <div class="text-h3 font-weight-bold text-white mb-2">50+</div>
                <div class="text-body-1 text-white opacity-80">Components</div>
              </v-col>
              <v-col cols="6" md="3" class="text-center">
                <div class="text-h3 font-weight-bold text-white mb-2">15+</div>
                <div class="text-body-1 text-white opacity-80">Views</div>
              </v-col>
              <v-col cols="6" md="3" class="text-center">
                <div class="text-h3 font-weight-bold text-white mb-2">100%</div>
                <div class="text-body-1 text-white opacity-80">Responsive</div>
              </v-col>
              <v-col cols="6" md="3" class="text-center">
                <div class="text-h3 font-weight-bold text-white mb-2">∞</div>
                <div class="text-body-1 text-white opacity-80">Possibilities</div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Contact Section -->
    <v-row>
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-text class="pa-8 text-center">
            <h3 class="text-h4 font-weight-bold mb-4">Get In Touch</h3>
            <p class="text-h6 font-weight-light text-grey mb-6">
              Have questions or want to learn more about our platform?
            </p>
            
            <div class="d-flex justify-center gap-4 flex-wrap">
              <v-btn
                color="primary"
                variant="flat"
                size="large"
                prepend-icon="mdi-email"
              >
                Contact Us
              </v-btn>
              <v-btn
                color="success"
                variant="outlined"
                size="large"
                prepend-icon="mdi-github"
              >
                View Source
              </v-btn>
              <v-btn
                color="info"
                variant="outlined"
                size="large"
                prepend-icon="mdi-book-open"
              >
                Documentation
              </v-btn>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script setup>
import { useHead } from '@unhead/vue'

// Set head tags
useHead({
  title: 'About - Dashboard',
  meta: [
    {
      name: 'description',
      content: 'Learn about our modern dashboard platform built with Vue 3, Vuetify 3, and advanced security features.'
    }
  ]
})
</script>

<style scoped>
.about-view {
  padding: 0;
}

.hero-card {
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)) 0%, #2d1b69 100%) !important;
  border-radius: 16px;
}

.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stats-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 16px;
}

/* Animation for feature cards */
.feature-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Stagger animation for feature cards */
.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

/* Dark theme adjustments */
:global(.v-theme--dark) .hero-card {
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)) 0%, #1a1a2e 100%) !important;
}

:global(.v-theme--dark) .stats-card {
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%) !important;
}

/* Responsive adjustments */
@media (max-width: 960px) {
  .hero-card .v-card-text {
    padding: 24px !important;
  }
  
  .feature-card .v-card-text {
    padding: 24px !important;
  }
}
</style>
