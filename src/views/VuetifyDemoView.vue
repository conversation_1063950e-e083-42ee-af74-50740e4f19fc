<template>
  <v-container fluid class="pa-6">
    <v-row>
      <v-col cols="12">
        <v-card class="mb-6" elevation="2">
          <v-card-title class="text-h4 text-primary">
            <v-icon start>mdi-vuetify</v-icon>
            Vuetify 3 Theme Switcher Demo
          </v-card-title>
          <v-card-text>
            <v-alert type="success" variant="tonal" class="mb-4">
              <v-icon start>mdi-check-circle</v-icon>
              Vuetify 3 has been successfully integrated! Click the settings button (⚙️) in the bottom-right corner to test the theme switcher.
            </v-alert>
            
            <p class="text-body-1 mb-4">
              This demo showcases the new Vuetify 3 implementation that replicates all the functionality 
              of the original Bootstrap-based theme switcher. The new implementation provides:
            </p>
            
            <v-list class="mb-4">
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-check</v-icon>
                </template>
                <v-list-item-title>Complete theme management with Pinia store</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-check</v-icon>
                </template>
                <v-list-item-title>Layout width controls (boxed/full)</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-check</v-icon>
                </template>
                <v-list-item-title>Menu style options (default/icon-hover/icon-default)</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-check</v-icon>
                </template>
                <v-list-item-title>Position controls for menu and header</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-check</v-icon>
                </template>
                <v-list-item-title>Comprehensive color theming system</v-list-item-title>
              </v-list-item>
              <v-list-item>
                <template #prepend>
                  <v-icon color="success">mdi-check</v-icon>
                </template>
                <v-list-item-title>Dark/light mode with localStorage persistence</v-list-item-title>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12" md="6">
        <v-card elevation="2">
          <v-card-title class="text-h5">
            <v-icon start color="primary">mdi-palette</v-icon>
            Current Theme Settings
          </v-card-title>
          <v-card-text>
            <v-table density="compact">
              <tbody>
                <tr>
                  <td class="font-weight-medium">Theme Mode:</td>
                  <td>
                    <v-chip 
                      :color="themeStore.isDarkMode ? 'grey-darken-2' : 'amber-lighten-2'"
                      :prepend-icon="themeStore.isDarkMode ? 'mdi-weather-night' : 'mdi-weather-sunny'"
                      size="small"
                    >
                      {{ themeStore.activeSelections.themeMode }}
                    </v-chip>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Layout Width:</td>
                  <td>
                    <v-chip 
                      :color="themeStore.isBoxedLayout ? 'blue' : 'green'"
                      size="small"
                    >
                      {{ themeStore.activeSelections.layoutWidth }}
                    </v-chip>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Menu Style:</td>
                  <td>
                    <v-chip color="purple" size="small">
                      {{ themeStore.activeSelections.menuStyle }}
                    </v-chip>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Menu Position:</td>
                  <td>
                    <v-chip color="orange" size="small">
                      {{ themeStore.activeSelections.menuPosition }}
                    </v-chip>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Header Position:</td>
                  <td>
                    <v-chip color="teal" size="small">
                      {{ themeStore.activeSelections.headerPosition }}
                    </v-chip>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Loader:</td>
                  <td>
                    <v-chip 
                      :color="themeStore.isLoaderEnabled ? 'success' : 'error'"
                      size="small"
                    >
                      {{ themeStore.activeSelections.loader }}
                    </v-chip>
                  </td>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" md="6">
        <v-card elevation="2">
          <v-card-title class="text-h5">
            <v-icon start color="secondary">mdi-format-color-fill</v-icon>
            Color Settings
          </v-card-title>
          <v-card-text>
            <v-table density="compact">
              <tbody>
                <tr>
                  <td class="font-weight-medium">Menu Color:</td>
                  <td>
                    <div class="d-flex align-center">
                      <div 
                        class="color-preview me-2"
                        :style="{ backgroundColor: themeStore.colorDefinitions.menu[themeStore.activeSelections.menuColor] }"
                      ></div>
                      <span>{{ themeStore.activeSelections.menuColor }}</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Header Color:</td>
                  <td>
                    <div class="d-flex align-center">
                      <div 
                        class="color-preview me-2"
                        :style="{ backgroundColor: themeStore.colorDefinitions.header[themeStore.activeSelections.headerColor] }"
                      ></div>
                      <span>{{ themeStore.activeSelections.headerColor }}</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Primary Color:</td>
                  <td>
                    <div class="d-flex align-center">
                      <div 
                        class="color-preview me-2"
                        :style="{ backgroundColor: themeStore.colorDefinitions.primary[themeStore.activeSelections.primaryColor] }"
                      ></div>
                      <span>{{ themeStore.activeSelections.primaryColor }}</span>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="font-weight-medium">Background Color:</td>
                  <td>
                    <div class="d-flex align-center">
                      <div 
                        class="color-preview me-2"
                        :style="{ backgroundColor: themeStore.colorDefinitions.background[themeStore.activeSelections.backgroundColor] }"
                      ></div>
                      <span>{{ themeStore.activeSelections.backgroundColor }}</span>
                    </div>
                  </td>
                </tr>
              </tbody>
            </v-table>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <v-row>
      <v-col cols="12">
        <v-card elevation="2">
          <v-card-title class="text-h5">
            <v-icon start color="info">mdi-test-tube</v-icon>
            Component Showcase
          </v-card-title>
          <v-card-text>
            <p class="text-body-1 mb-4">
              Here are some Vuetify components to demonstrate the theming system:
            </p>
            
            <v-row>
              <v-col cols="12" sm="6" md="3">
                <v-btn color="primary" block class="mb-2">Primary Button</v-btn>
                <v-btn color="secondary" block class="mb-2">Secondary Button</v-btn>
                <v-btn color="success" block class="mb-2">Success Button</v-btn>
                <v-btn color="error" block>Error Button</v-btn>
              </v-col>
              
              <v-col cols="12" sm="6" md="3">
                <v-text-field
                  label="Sample Input"
                  variant="outlined"
                  class="mb-2"
                />
                <v-select
                  label="Sample Select"
                  :items="['Option 1', 'Option 2', 'Option 3']"
                  variant="outlined"
                />
              </v-col>
              
              <v-col cols="12" sm="6" md="3">
                <v-switch
                  label="Sample Switch"
                  color="primary"
                  class="mb-2"
                />
                <v-checkbox
                  label="Sample Checkbox"
                  color="primary"
                  class="mb-2"
                />
                <v-radio-group inline>
                  <v-radio label="Option A" value="a" />
                  <v-radio label="Option B" value="b" />
                </v-radio-group>
              </v-col>
              
              <v-col cols="12" sm="6" md="3">
                <v-progress-linear
                  color="primary"
                  :model-value="75"
                  class="mb-2"
                />
                <v-progress-circular
                  color="secondary"
                  :model-value="60"
                  class="mb-2"
                />
                <v-rating
                  :model-value="4"
                  color="amber"
                  half-increments
                />
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { useThemeStore } from '@/stores/theme'

// Theme store
const themeStore = useThemeStore()
</script>

<style scoped>
.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
