<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import LoginForm from '@/components/auth/LoginForm.vue'

const router = useRouter()
const { isAuthenticated } = useAuth()

const showSuccessToast = ref(false)

// Redirect if already authenticated
onMounted(() => {
  if (isAuthenticated.value) {
    router.push('/')
  }
})

const handleLoginSuccess = (user) => {
  showSuccessToast.value = true

  // Hide toast and redirect after a short delay
  setTimeout(() => {
    showSuccessToast.value = false
    router.push('/')
  }, 1500)
}

const handleLoginError = (error) => {
  console.error('Login error:', error)
  // Error is already handled by the LoginForm component
}
</script>

<template>
  <div class="login-view">
    <div class="container-fluid">
      <div class="row min-vh-100">
        <!-- Left side - Branding/Info -->
        <div class="col-lg-6 d-none d-lg-flex align-items-center justify-content-center bg-primary">
          <div class="text-center text-white">
            <h1 class="display-4 mb-4">Welcome Back!</h1>
            <p class="lead mb-4">
              Sign in to access your dashboard and manage your account with role-based permissions.
            </p>
            <div class="feature-list">
              <div class="feature-item mb-3">
                <i class="icon-shield-check me-2"></i>
                Secure Authentication
              </div>
              <div class="feature-item mb-3">
                <i class="icon-users me-2"></i>
                Role-Based Access Control
              </div>
              <div class="feature-item mb-3">
                <i class="icon-lock me-2"></i>
                Permission Management
              </div>
            </div>
          </div>
        </div>

        <!-- Right side - Login Form -->
        <div class="col-lg-6 d-flex align-items-center justify-content-center">
          <div class="w-100" style="max-width: 400px">
            <LoginForm @success="handleLoginSuccess" @error="handleLoginError" />
          </div>
        </div>
      </div>
    </div>

    <!-- Success Toast -->
    <div v-if="showSuccessToast" class="toast-container position-fixed top-0 end-0 p-3">
      <div class="toast show" role="alert">
        <div class="toast-header">
          <i class="icon-check-circle text-success me-2"></i>
          <strong class="me-auto">Success</strong>
          <button type="button" class="btn-close" @click="showSuccessToast = false"></button>
        </div>
        <div class="toast-body">Login successful! Redirecting...</div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.login-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-primary {
  background: linear-gradient(135deg, var(--Primary, #161326) 0%, #2d1b69 100%) !important;
}

.feature-item {
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-item i {
  font-size: 1.3rem;
}

.toast-container {
  z-index: 1055;
}

.toast {
  background-color: white;
  border: 1px solid #dee2e6;
}

@media (max-width: 991.98px) {
  .login-view {
    background: white;
  }
}
</style>
