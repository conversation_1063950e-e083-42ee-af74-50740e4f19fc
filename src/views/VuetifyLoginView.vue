<template>
  <v-app>
    <v-main class="login-main">
      <v-container fluid class="fill-height pa-0">
        <v-row no-gutters class="fill-height">
          <!-- Left Side - Branding -->
          <v-col
            cols="12"
            lg="6"
            class="d-none d-lg-flex"
          >
            <v-card
              color="primary"
              variant="flat"
              class="fill-height d-flex align-center justify-center text-white branding-section"
            >
              <div class="text-center pa-8">
                <v-img
                  src="/images/logo/logo-dark.svg"
                  max-width="200"
                  class="mx-auto mb-8"
                />
                
                <h1 class="text-h3 font-weight-bold mb-6">Welcome Back!</h1>
                <p class="text-h6 font-weight-light mb-8 opacity-90">
                  Sign in to access your dashboard and manage your account with role-based permissions.
                </p>
                
                <v-list class="bg-transparent text-white feature-list">
                  <v-list-item class="feature-item">
                    <template #prepend>
                      <v-icon color="white" size="24">mdi-shield-check</v-icon>
                    </template>
                    <v-list-item-title class="text-h6 font-weight-medium">
                      Secure Authentication
                    </v-list-item-title>
                    <v-list-item-subtitle class="text-white opacity-80">
                      Advanced security with encrypted data protection
                    </v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item class="feature-item">
                    <template #prepend>
                      <v-icon color="white" size="24">mdi-account-group</v-icon>
                    </template>
                    <v-list-item-title class="text-h6 font-weight-medium">
                      Role-Based Access Control
                    </v-list-item-title>
                    <v-list-item-subtitle class="text-white opacity-80">
                      Granular permissions for different user roles
                    </v-list-item-subtitle>
                  </v-list-item>
                  
                  <v-list-item class="feature-item">
                    <template #prepend>
                      <v-icon color="white" size="24">mdi-cog</v-icon>
                    </template>
                    <v-list-item-title class="text-h6 font-weight-medium">
                      Permission Management
                    </v-list-item-title>
                    <v-list-item-subtitle class="text-white opacity-80">
                      Fine-tuned access control for all features
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </div>
            </v-card>
          </v-col>

          <!-- Right Side - Login Form -->
          <v-col
            cols="12"
            lg="6"
            class="d-flex align-center justify-center"
          >
            <v-card
              class="login-form-card pa-8"
              max-width="450"
              width="100%"
              elevation="0"
            >
              <div class="text-center mb-8">
                <v-img
                  src="/images/logo/logo.svg"
                  max-width="150"
                  class="mx-auto mb-4 d-lg-none"
                />
                <h2 class="text-h4 font-weight-bold mb-2">Sign In</h2>
                <p class="text-body-1 text-grey">Enter your credentials to access your account</p>
              </div>

              <VuetifyLoginForm 
                @success="handleLoginSuccess" 
                @error="handleLoginError" 
              />

              <v-divider class="my-6" />

              <div class="text-center">
                <p class="text-body-2 text-grey mb-4">
                  Don't have an account?
                </p>
                <v-btn
                  :to="'/sign-up'"
                  color="primary"
                  variant="outlined"
                  block
                >
                  Create Account
                </v-btn>
              </div>

              <!-- Demo Credentials -->
              <v-expansion-panels class="mt-6" variant="accordion">
                <v-expansion-panel>
                  <v-expansion-panel-title>
                    <v-icon start>mdi-information</v-icon>
                    Demo Credentials
                  </v-expansion-panel-title>
                  <v-expansion-panel-text>
                    <v-alert
                      type="info"
                      variant="tonal"
                      class="mb-4"
                    >
                      Use these credentials to test different user roles:
                    </v-alert>
                    
                    <v-list density="compact">
                      <v-list-item>
                        <v-list-item-title class="font-weight-medium">Admin User</v-list-item-title>
                        <v-list-item-subtitle>
                          Email: <EMAIL><br>
                          Password: admin123
                        </v-list-item-subtitle>
                        <template #append>
                          <v-chip color="error" size="small">Admin</v-chip>
                        </template>
                      </v-list-item>
                      
                      <v-list-item>
                        <v-list-item-title class="font-weight-medium">Moderator User</v-list-item-title>
                        <v-list-item-subtitle>
                          Email: <EMAIL><br>
                          Password: mod123
                        </v-list-item-subtitle>
                        <template #append>
                          <v-chip color="warning" size="small">Moderator</v-chip>
                        </template>
                      </v-list-item>
                      
                      <v-list-item>
                        <v-list-item-title class="font-weight-medium">Regular User</v-list-item-title>
                        <v-list-item-subtitle>
                          Email: <EMAIL><br>
                          Password: user123
                        </v-list-item-subtitle>
                        <template #append>
                          <v-chip color="success" size="small">User</v-chip>
                        </template>
                      </v-list-item>
                    </v-list>
                  </v-expansion-panel-text>
                </v-expansion-panel>
              </v-expansion-panels>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </v-main>

    <!-- Success Snackbar -->
    <v-snackbar
      v-model="showSuccessSnackbar"
      color="success"
      timeout="3000"
      location="top"
    >
      <v-icon start>mdi-check-circle</v-icon>
      Login successful! Redirecting...
      
      <template #actions>
        <v-btn
          variant="text"
          @click="showSuccessSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>

    <!-- Error Snackbar -->
    <v-snackbar
      v-model="showErrorSnackbar"
      color="error"
      timeout="5000"
      location="top"
    >
      <v-icon start>mdi-alert-circle</v-icon>
      {{ errorMessage }}
      
      <template #actions>
        <v-btn
          variant="text"
          @click="showErrorSnackbar = false"
        >
          Close
        </v-btn>
      </template>
    </v-snackbar>
  </v-app>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuth } from '@/composables/useAuth'
import VuetifyLoginForm from '@/components/auth/VuetifyLoginForm.vue'

const router = useRouter()
const { isAuthenticated } = useAuth()

const showSuccessSnackbar = ref(false)
const showErrorSnackbar = ref(false)
const errorMessage = ref('')

// Redirect if already authenticated
onMounted(() => {
  if (isAuthenticated.value) {
    router.push('/')
  }
})

const handleLoginSuccess = (user) => {
  showSuccessSnackbar.value = true
  
  // Redirect after a short delay
  setTimeout(() => {
    showSuccessSnackbar.value = false
    router.push('/')
  }, 1500)
}

const handleLoginError = (error) => {
  errorMessage.value = error.message || 'Login failed. Please try again.'
  showErrorSnackbar.value = true
  console.error('Login error:', error)
}
</script>

<style scoped>
.login-main {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.branding-section {
  background: linear-gradient(135deg, rgb(var(--v-theme-primary)) 0%, #2d1b69 100%) !important;
}

.login-form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.feature-list {
  background: transparent !important;
}

.feature-item {
  margin-bottom: 16px;
  padding: 16px 0;
}

.feature-item .v-list-item__prepend {
  margin-right: 16px;
}

/* Dark theme adjustments */
:global(.v-theme--dark) .login-main {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
}

:global(.v-theme--dark) .login-form-card {
  background: rgba(var(--v-theme-surface), 0.95) !important;
  color: rgb(var(--v-theme-on-surface));
}

/* Mobile responsiveness */
@media (max-width: 960px) {
  .login-main {
    background: rgb(var(--v-theme-background));
  }
  
  .login-form-card {
    background: rgb(var(--v-theme-surface));
    box-shadow: none;
    border-radius: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
}

/* Animation for smooth transitions */
.login-form-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.branding-section > div {
  animation: fadeIn 0.8s ease-out 0.2s both;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
