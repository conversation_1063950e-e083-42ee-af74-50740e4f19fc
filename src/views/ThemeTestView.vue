<script setup>
import { useHead } from '@unhead/vue'

// Set head tags for theme test page
useHead({
  title: 'Theme Test',
  meta: [
    {
      name: 'description',
      content: 'Test page for theme switching functionality'
    }
  ]
})
</script>

<template>
  <div class="main-content-inner">
    <div class="main-content-wrap">
      <div class="tf-container">
        <div class="row">
          <div class="col-12">
            <div class="wg-box mb-4">
              <h1>Theme Switcher Test Page</h1>
              <p>This page demonstrates the theme switching functionality.</p>
              
              <div class="alert alert-info mb-4">
                <h4>How to Test:</h4>
                <ol>
                  <li><strong>Dark Mode Toggle:</strong> Use the toggle switch in the header to switch between light and dark modes</li>
                  <li><strong>Advanced Theme Settings:</strong> Click the settings icon (⚙️) in the header to open the theme customization panel</li>
                  <li><strong>Color Customization:</strong> In the settings panel, you can customize:
                    <ul>
                      <li>Menu background colors</li>
                      <li>Header background colors</li>
                      <li>Primary theme colors</li>
                      <li>Overall background colors</li>
                    </ul>
                  </li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <div class="row">
          <div class="col-md-6">
            <div class="wg-box">
              <h3>Sample Card 1</h3>
              <p>This is a sample card to demonstrate how the theme affects different components.</p>
              <button class="tf-button style-1">Sample Button</button>
            </div>
          </div>
          <div class="col-md-6">
            <div class="wg-box">
              <h3>Sample Card 2</h3>
              <p>Another sample card with different content to show theme consistency.</p>
              <div class="form-group">
                <label>Sample Input:</label>
                <input type="text" class="form-control" placeholder="Type something...">
              </div>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-12">
            <div class="wg-box">
              <h3>Sample Table</h3>
              <table class="table">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Value</th>
                    <th>Status</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td>Item 1</td>
                    <td>$100.00</td>
                    <td><span class="badge bg-success">Active</span></td>
                  </tr>
                  <tr>
                    <td>Item 2</td>
                    <td>$250.00</td>
                    <td><span class="badge bg-warning">Pending</span></td>
                  </tr>
                  <tr>
                    <td>Item 3</td>
                    <td>$75.00</td>
                    <td><span class="badge bg-danger">Inactive</span></td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <div class="row mt-4">
          <div class="col-12">
            <div class="wg-box">
              <h3>Theme Features</h3>
              <div class="row">
                <div class="col-md-3">
                  <div class="feature-item text-center p-3">
                    <i class="icon-sun" style="font-size: 2rem; color: #f59e0b;"></i>
                    <h5>Light Mode</h5>
                    <p>Clean and bright interface</p>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="feature-item text-center p-3">
                    <i class="icon-moon" style="font-size: 2rem; color: #6366f1;"></i>
                    <h5>Dark Mode</h5>
                    <p>Easy on the eyes</p>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="feature-item text-center p-3">
                    <i class="icon-setting1" style="font-size: 2rem; color: #10b981;"></i>
                    <h5>Customizable</h5>
                    <p>Multiple color options</p>
                  </div>
                </div>
                <div class="col-md-3">
                  <div class="feature-item text-center p-3">
                    <i class="icon-save" style="font-size: 2rem; color: #f97316;"></i>
                    <h5>Persistent</h5>
                    <p>Saves your preferences</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.alert {
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

.feature-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.bg-success {
  background-color: #10b981;
  color: white;
}

.bg-warning {
  background-color: #f59e0b;
  color: white;
}

.bg-danger {
  background-color: #ef4444;
  color: white;
}

.table {
  width: 100%;
  margin-bottom: 1rem;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e7eb;
}

.table th {
  font-weight: 600;
  background-color: #f9fafb;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 1rem;
}

.form-control:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
</style>
