<script setup lang="ts">
import SmallChartSummary from '@/components/SmallChartSummary.vue'
import LineChartDebug from '@/components/LineChartDebug.vue'
import SmallChartBackgroundDemo from '@/components/SmallChartBackgroundDemo.vue'
import ChartBackgroundDemo from '@/components/ChartBackgroundDemo.vue'
import LineChartSolution from '@/components/LineChartSolution.vue'
import ApexChart from '@/components/shared/ApexChart.vue'
import FinalChartTest from '@/components/FinalChartTest.vue'
import ChartShowcase from '@/components/ChartShowcase.vue'
</script>

<template>
  <!-- Dynamic ApexChart using chart-type -->
  <ApexChart
    chart-id="line-chart-twoline-dynamic"
    chart-type="line-chart-twoline-improved"
    :options="{
      chart: {
        height: 350,
        background: '#f8f9fa', // Light gray background
        // Or try: '#1a1a1a' for dark, '#e3f2fd' for light blue
      },
      plotOptions: {
        area: {
          fillTo: 'end',
        },
      },
    }"
  />

  <!-- Additional dynamic chart examples -->
  <div class="row mt-4">
    <div class="col-lg-6">
      <div class="wg-box style-1 bg-Gainsboro shadow-none mb-32">
        <div class="title mb-16">
          <div class="label-01">Dynamic Candlestick Chart</div>
        </div>
        <ApexChart
          chart-id="dynamic-candlestick"
          chart-type="candlestick-1"
          :options="{
            chart: { height: 350 },
            xaxis: {
              type: 'datetime',
              labels: { show: true },
            },
            yaxis: { show: true },
          }"
        />
      </div>
    </div>
    <div class="col-lg-6">
      <div class="wg-box style-1 bg-Gainsboro shadow-none mb-32">
        <div class="title mb-16">
          <div class="label-01">Dynamic Column Chart</div>
        </div>
        <ApexChart
          chart-id="dynamic-column"
          chart-type="column-chart-1"
          :options="{
            chart: { height: 300 },
            xaxis: { labels: { show: true } },
            yaxis: { show: true },
            dataLabels: { enabled: true },
          }"
        />
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-lg-4">
      <div class="wg-box style-1 bg-Gainsboro shadow-none mb-32">
        <div class="title mb-16">
          <div class="label-01">Dynamic Donut Chart</div>
        </div>
        <ApexChart chart-id="dynamic-donut" chart-type="donut-1" />
      </div>
    </div>
    <div class="col-lg-4">
      <div class="wg-box style-1 bg-Gainsboro shadow-none mb-32">
        <div class="title mb-16">
          <div class="label-01">Dynamic Line Chart</div>
        </div>
        <ApexChart
          chart-id="dynamic-line"
          chart-type="line-chart-1"
          :options="{
            chart: { height: 250 },
          }"
        />
      </div>
    </div>
    <div class="col-lg-4">
      <div class="wg-box style-1 bg-Gainsboro shadow-none mb-32">
        <div class="title mb-16">
          <div class="label-01">Dynamic Small Chart</div>
        </div>
        <ApexChart chart-id="dynamic-small" chart-type="small-chart-1" />
      </div>
    </div>
  </div>

  <!-- Small Chart Summary -->
  <SmallChartSummary />

  <!-- Small Chart Background Demo -->
  <SmallChartBackgroundDemo />

  <!-- Chart Background Color Demo -->
  <ChartBackgroundDemo />

  <!-- Line Chart Solution -->
  <LineChartSolution />

  <!-- Line Chart Debug -->
  <LineChartDebug />

  <!-- Final Chart Test - Should work now! -->
  <FinalChartTest />

  <!-- Dynamic Chart Showcase -->
  <ChartShowcase />
</template>

<style scoped></style>
