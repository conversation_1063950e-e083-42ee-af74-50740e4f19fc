<script setup>
import { computed, watch, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useHead } from '@unhead/vue'
import { useAuth } from '@/composables/useAuth'
import CanAccess from '@/components/auth/CanAccess.vue'
import Switcher from '@/components/shared/Switcher.vue'
import defaultAvatar from '@/assets/images/avatar/user-1.png'

const props = defineProps({
  collapsed: Boolean,
})

// declare the event signature
const emit = defineEmits(['update:collapsed'])

const router = useRouter()
const route = useRoute()
const { user, userName, userRole, logout } = useAuth()

// Initialize head management
const head = useHead({
  titleTemplate: '%s | Dashboard App',
  meta: [
    {
      name: 'description',
      content:
        'Modern dashboard application with user authentication and role-based access control',
    },
  ],
})

// Helper functions for updating head
const setTitle = (title) => {
  head.patch({ title })
}

const setDescription = (description) => {
  head.patch({
    meta: [{ name: 'description', content: description }],
  })
}

function toggleSidebar() {
  // emit the new value
  emit('update:collapsed', !props.collapsed)
}

const handleLogout = async () => {
  await logout()
  router.push('/login')
}

const userAvatar = computed(() => {
  return defaultAvatar
  // return user.value?.avatar || defaultAvatar
})

// Dynamic page title based on route
const pageTitle = computed(() => {
  const routeMeta = route.meta
  const routeName = route.name

  // Check if route has a custom title in meta
  if (routeMeta?.title) {
    return routeMeta.title
  }

  // Generate title based on route name
  switch (routeName) {
    case 'home':
      return 'Home'
    case 'dashboard':
      return 'Dashboard'
    case 'admin':
      return 'Admin Panel'
    case 'profile':
      return 'Profile'
    case 'about':
      return 'About'
    case 'login':
      return 'Login'
    default:
      return 'Dashboard'
  }
})

// Watch for route changes and update head tags
watch(
  () => route.name,
  () => {
    setTitle(pageTitle.value)

    // Set page-specific descriptions
    const descriptions = {
      home: 'Welcome to your dashboard home page',
      dashboard: 'View your dashboard analytics and overview',
      admin: 'Administrative panel for system management',
      profile: 'Manage your user profile and settings',
      about: 'Learn more about our application',
      login: 'Sign in to your account',
    }

    const description = descriptions[route.name] || 'Dashboard application'
    setDescription(description)
  },
  { immediate: true },
)

// Debug the initial state and initialize theme functionality
onMounted(() => {
  console.log('NavBar mounted - checking initial state...')
  console.log('Wrapper element:', document.querySelector('#wrapper'))
  console.log('Offcanvas element:', document.querySelector('#offcanvasRight'))
  console.log('Settings button:', document.querySelector('.setting'))

  // Wait for offcanvas to be injected, then initialize theme functionality
  setTimeout(() => {
    initializeThemeFunctionality()
  }, 1000)
})

// Initialize theme functionality directly in NavBar
const initializeThemeFunctionality = () => {
  console.log('NavBar: Initializing theme functionality...')

  // Import jQuery and initialize
  import('jquery').then(({ default: $ }) => {
    console.log('NavBar: jQuery loaded, setting up event handlers...')

    // Tab switching - Direct approach
    $(document).on('click', '.widget-menu-tab .item-title', function() {
      console.log('Tab clicked')
      const $this = $(this)
      const tabText = $this.find('.body-title').text().trim()

      console.log('Tab clicked:', tabText)

      // Remove active class from all tabs
      $('.widget-menu-tab .item-title').removeClass('active')
      $this.addClass('active')

      // Hide all content panels
      $('.widget-content-inner').removeClass('active').hide()

      // Show the correct content based on tab text
      if (tabText === 'Theme Style') {
        console.log('Showing Theme Style content')
        // Find and show the panel with form-theme-style
        const $stylePanel = $('.widget-content-inner').filter(function() {
          return $(this).find('.form-theme-style').length > 0
        })
        $stylePanel.addClass('active').show()
        console.log('Style panel found:', $stylePanel.length > 0)
      } else if (tabText === 'Theme Colors') {
        console.log('Showing Theme Colors content')
        // Find and show the panel with form-theme-color
        const $colorPanel = $('.widget-content-inner').filter(function() {
          return $(this).find('.form-theme-color').length > 0
        })
        $colorPanel.addClass('active').show()
        console.log('Color panel found:', $colorPanel.length > 0)
      }

      // Debug final state
      setTimeout(() => {
        $('.widget-content-inner').each(function(i) {
          const isVisible = $(this).is(':visible')
          const contentType = $(this).find('.form-theme-style').length > 0 ? 'Style' :
                             $(this).find('.form-theme-color').length > 0 ? 'Colors' : 'Unknown'
          console.log(`Panel ${i} (${contentType}): visible=${isVisible}`)
        })
      }, 10)
    })

    // Color selections - Menu colors
    $(document).on('click', '.colors-menu .item', function() {
      console.log('Menu color clicked')
      const $this = $(this)
      $('.colors-menu .item').removeClass('active')
      $this.addClass('active')

      if ($this.hasClass('color-fff')) {
        $('.layout-wrap').attr('data-menu-background', 'colors-menu-fff')
        console.log('Menu color: white')
      } else if ($this.hasClass('color-1E293B')) {
        $('.layout-wrap').attr('data-menu-background', 'colors-menu-1E293B')
        console.log('Menu color: dark blue')
      } else if ($this.hasClass('color-161326')) {
        $('.layout-wrap').attr('data-menu-background', 'colors-menu-161326')
        console.log('Menu color: dark')
      } else if ($this.hasClass('color-3A3043')) {
        $('.layout-wrap').attr('data-menu-background', 'colors-menu-3A3043')
        console.log('Menu color: purple')
      }
    })

    // Color selections - Header colors
    $(document).on('click', '.colors-header .item', function() {
      console.log('Header color clicked')
      const $this = $(this)
      $('.colors-header .item').removeClass('active')
      $this.addClass('active')

      if ($this.hasClass('color-fff')) {
        $('.layout-wrap').attr('data-colors-header', 'colors-header-fff')
      } else if ($this.hasClass('color-1E293B')) {
        $('.layout-wrap').attr('data-colors-header', 'colors-header-1E293B')
      } else if ($this.hasClass('color-161326')) {
        $('.layout-wrap').attr('data-colors-header', 'colors-header-161326')
      } else if ($this.hasClass('color-3A3043')) {
        $('.layout-wrap').attr('data-colors-header', 'colors-header-3A3043')
      }
    })

    // Color selections - Primary colors
    $(document).on('click', '.colors-theme-primary .item', function() {
      console.log('Primary color clicked')
      const $this = $(this)
      $('.colors-theme-primary .item').removeClass('active')
      $this.addClass('active')

      if ($this.hasClass('color-2377FC')) {
        $('.layout-wrap').attr('data-theme-primary', 'theme-primary-2377FC')
      } else if ($this.hasClass('color-161326')) {
        $('.layout-wrap').attr('data-theme-primary', 'theme-primary-161326')
      } else if ($this.hasClass('color-35988D')) {
        $('.layout-wrap').attr('data-theme-primary', 'theme-primary-35988D')
      } else if ($this.hasClass('color-7047D6')) {
        $('.layout-wrap').attr('data-theme-primary', 'theme-primary-7047D6')
      }
    })

    // Color selections - Background colors
    $(document).on('click', '.colors-theme-background .item', function() {
      console.log('Background color clicked')
      const $this = $(this)
      $('.colors-theme-background .item').removeClass('active')
      $this.addClass('active')

      if ($this.hasClass('color-FFFFFF')) {
        $('body').attr('data-theme-background', 'theme-background-FFFFFF')
      } else if ($this.hasClass('color-252E3A')) {
        $('body').attr('data-theme-background', 'theme-background-252E3A')
      } else if ($this.hasClass('color-1E1D2A')) {
        $('body').attr('data-theme-background', 'theme-background-1E1D2A')
      } else if ($this.hasClass('color-1B2627')) {
        $('body').attr('data-theme-background', 'theme-background-1B2627')
      }
    })

    // Clear all buttons
    $(document).on('click', '.button-clear-select', function() {
      console.log('Clear all clicked')
      const $this = $(this)

      if ($this.closest('.form-theme-style').length) {
        console.log('Clearing style settings')
        // Reset layout classes
        $('.layout-wrap').removeClass('layout-width-boxed menu-style-icon menu-style-icon-default menu-position-scrollable header-position-scrollable loader-off')
      } else if ($this.closest('.form-theme-color').length) {
        console.log('Clearing color settings')
        // Reset colors
        $('.layout-wrap').removeAttr('data-menu-background data-colors-header data-theme-primary')
        $('body').removeAttr('data-theme-background')

        // Reset active states
        $('.colors-menu .item').removeClass('active')
        $('.colors-menu .color-161326').addClass('active')
        $('.colors-header .item').removeClass('active')
        $('.colors-header .color-fff').addClass('active')
        $('.colors-theme-primary .item').removeClass('active')
        $('.colors-theme-primary .color-161326').addClass('active')
        $('.colors-theme-background .item').removeClass('active')
        $('.colors-theme-background .color-FFFFFF').addClass('active')
      }
    })

    // Dark/Light theme toggle
    $(document).on('click', '.theme-dark-light .light input', function() {
      console.log('Light theme selected')
      $('body').removeClass('dark-theme')
      localStorage.toggled = 'light-theme'
    })

    $(document).on('click', '.theme-dark-light .dark input', function() {
      console.log('Dark theme selected')
      $('body').addClass('dark-theme')
      localStorage.toggled = 'dark-theme'
    })

    // Close button
    $(document).on('click', '[data-bs-dismiss="offcanvas"]', function() {
      console.log('Close button clicked')
      const offcanvasElement = document.querySelector('#offcanvasRight')
      if (offcanvasElement) {
        if (window.bootstrap && window.bootstrap.Offcanvas) {
          const offcanvas = window.bootstrap.Offcanvas.getInstance(offcanvasElement)
          if (offcanvas) {
            offcanvas.hide()
          }
        } else {
          // Manual close
          offcanvasElement.classList.remove('show')
          offcanvasElement.style.visibility = 'hidden'
          offcanvasElement.style.transform = 'translateX(100%)'

          const backdrop = document.querySelector('.offcanvas-backdrop')
          if (backdrop) {
            backdrop.remove()
          }
        }
      }
    })

    // Initialize default tab state - Direct approach
    setTimeout(() => {
      console.log('Setting up default tab state...')

      // Debug content panels
      $('.widget-content-inner').each(function(index) {
        const $panel = $(this)
        const hasStyleForm = $panel.find('.form-theme-style').length > 0
        const hasColorForm = $panel.find('.form-theme-color').length > 0
        console.log(`Panel ${index}:`, {
          hasStyleForm,
          hasColorForm,
          content: hasStyleForm ? 'Theme Style' : hasColorForm ? 'Theme Colors' : 'Unknown'
        })
      })

      // Clear all states first
      $('.widget-menu-tab .item-title').removeClass('active')
      $('.widget-content-inner').removeClass('active').hide()

      // Set Theme Style tab as active (first tab)
      $('.widget-menu-tab .item-title').first().addClass('active')

      // Find and show the Theme Style content panel
      const $stylePanel = $('.widget-content-inner').filter(function() {
        return $(this).find('.form-theme-style').length > 0
      })

      if ($stylePanel.length > 0) {
        $stylePanel.addClass('active').show()
        console.log('Theme Style panel set as default')
      } else {
        console.warn('Theme Style panel not found, falling back to first panel')
        $('.widget-content-inner').first().addClass('active').show()
      }

      console.log('Default tab state set')

      // Initialize theme state from localStorage
      const savedTheme = localStorage.toggled
      if (savedTheme === 'dark-theme') {
        $('body').addClass('dark-theme')
        $('.theme-dark-light .dark input').prop('checked', true)
        $('.theme-dark-light .light input').prop('checked', false)
        console.log('Dark theme initialized from localStorage')
      } else {
        $('body').removeClass('dark-theme')
        $('.theme-dark-light .light input').prop('checked', true)
        $('.theme-dark-light .dark input').prop('checked', false)
        console.log('Light theme initialized (default or from localStorage)')
      }

      // Debug final state
      setTimeout(() => {
        $('.widget-content-inner').each(function(index) {
          const isVisible = $(this).is(':visible')
          const hasActive = $(this).hasClass('active')
          const contentType = $(this).find('.form-theme-style').length > 0 ? 'Style' :
                             $(this).find('.form-theme-color').length > 0 ? 'Colors' : 'Unknown'
          console.log(`Panel ${index} (${contentType}): visible=${isVisible}, active=${hasActive}`)
        })
      }, 50)
    }, 100)

    console.log('NavBar: Theme functionality initialized')
  }).catch(error => {
    console.error('NavBar: Failed to load jQuery:', error)
  })
}

// Theme switcher click handler
const openThemeSwitcher = () => {
  console.log('Theme switcher button clicked')

  try {
    // Find the offcanvas element from our Vue component
    const offcanvasElement = document.querySelector('#offcanvasRight')

    if (offcanvasElement) {
      console.log('Offcanvas element found, opening...')

      // Try Bootstrap method first
      if (window.bootstrap && window.bootstrap.Offcanvas) {
        const offcanvas = window.bootstrap.Offcanvas.getOrCreateInstance(offcanvasElement)
        offcanvas.show()
      } else {
        // Fallback: manual show
        offcanvasElement.classList.add('show')
        offcanvasElement.style.visibility = 'visible'
        offcanvasElement.style.transform = 'translateX(0)'

        // Add backdrop
        const backdrop = document.createElement('div')
        backdrop.className = 'offcanvas-backdrop fade show'
        backdrop.onclick = () => {
          offcanvasElement.classList.remove('show')
          offcanvasElement.style.visibility = 'hidden'
          offcanvasElement.style.transform = 'translateX(100%)'
          backdrop.remove()
        }
        document.body.appendChild(backdrop)
      }
    } else {
      console.error('Offcanvas element not found - make sure Switcher component is rendered')
    }
  } catch (error) {
    console.error('Error opening theme switcher:', error)
  }
}
</script>

<template>
  <div class="section-menu-left">
    <div class="box-logo">
      <a href="index.html" id="site-logo-inner">
        <img
          class=""
          id="logo_header"
          alt=""
          src="@/assets/images/logo/logo.svg"
          data-light="@/assets/images/logo/logo.svg"
          data-dark="@/assets/images/logo/logo-dark.svg"
        />
      </a>
      <div class="button-show-hide" @click="toggleSidebar">
        <i class="icon-back"></i>
      </div>
    </div>
    <div class="section-menu-left-wrap">
      <div class="center">
        <div class="center-item">
          <div class="center-heading f14-regular text-Gray menu-heading mb-12">Navigation</div>
        </div>
        <div class="center-item">
          <ul class="">
            <!-- Dashboard - Always visible (since NavBar only shows when authenticated) -->
            <li class="menu-item">
              <router-link to="/dashboard" class="menu-item-button">
                <div class="icon">
                  <i class="icon-category"></i>
                </div>
                <div class="text">Dashboard</div>
              </router-link>
            </li>

            <!-- Home - Always visible -->
            <li class="menu-item">
              <router-link to="/" class="menu-item-button">
                <div class="icon">
                  <i class="icon-home"></i>
                </div>
                <div class="text">Home</div>
              </router-link>
            </li>

            <!-- Admin Panel - Only for users with admin access -->
            <CanAccess action="read" subject="AdminPanel">
              <li class="menu-item">
                <router-link to="/admin" class="menu-item-button">
                  <div class="icon">
                    <i class="icon-shield"></i>
                  </div>
                  <div class="text">Admin Panel</div>
                </router-link>
              </li>
            </CanAccess>

            <!-- My Wallet - Always visible (since NavBar only shows when authenticated) -->
            <li class="menu-item has-children">
              <a href="javascript:void(0);" class="menu-item-button">
                <div class="icon">
                  <i class="icon-wallet1"></i>
                </div>
                <div class="text">My Wallet</div>
              </a>
              <ul class="sub-menu">
                <li class="sub-menu-item">
                  <a href="my-wallet.html" class="">
                    <div class="text">My Wallet</div>
                  </a>
                </li>
                <li class="sub-menu-item">
                  <router-link to="/profile" class="">
                    <div class="text">Profile</div>
                  </router-link>
                </li>
              </ul>
            </li>

            <!-- Transaction - Always visible (since NavBar only shows when authenticated) -->
            <li class="menu-item">
              <a href="transaction.html" class="menu-item-button">
                <div class="icon">
                  <svg
                    class=""
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M6.1428 8.50146V14.2182"
                      stroke="#A4A4A9"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M10.0317 5.76562V14.2179"
                      stroke="#A4A4A9"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      d="M13.8572 11.522V14.2178"
                      stroke="#A4A4A9"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                    <path
                      fill-rule="evenodd"
                      clip-rule="evenodd"
                      d="M13.9047 1.6665H6.0952C3.37297 1.6665 1.66663 3.59324 1.66663 6.3208V13.6789C1.66663 16.4064 3.36504 18.3332 6.0952 18.3332H13.9047C16.6349 18.3332 18.3333 16.4064 18.3333 13.6789V6.3208C18.3333 3.59324 16.6349 1.6665 13.9047 1.6665Z"
                      stroke="#A4A4A9"
                      stroke-width="1.5"
                      stroke-linecap="round"
                      stroke-linejoin="round"
                    />
                  </svg>
                </div>
                <div class="text">Transaction</div>
              </a>
            </li>
            <!-- Crypto - Always visible (since NavBar only shows when authenticated) -->
            <li class="menu-item">
              <a href="crypto.html" class="menu-item-button">
                <div class="icon">
                  <i class="icon-dash1"></i>
                </div>
                <div class="text">Crypto</div>
              </a>
            </li>

            <!-- Exchange - Always visible (since NavBar only shows when authenticated) -->
            <li class="menu-item">
              <a href="exchange.html" class="menu-item-button">
                <div class="icon">
                  <i class="icon-arrow-swap"></i>
                </div>
                <div class="text">Exchange</div>
              </a>
            </li>

            <!-- Settings - For users with settings access -->
            <CanAccess action="read" subject="Settings">
              <li class="menu-item">
                <a href="settings.html" class="menu-item-button">
                  <div class="icon">
                    <i class="icon-setting1"></i>
                  </div>
                  <div class="text">Settings</div>
                </a>
              </li>
            </CanAccess>

            <!-- Layout Test - Always visible -->
            <li class="menu-item">
              <router-link to="/layout-test" class="menu-item-button">
                <div class="icon">
                  <i class="icon-layout"></i>
                </div>
                <div class="text">Layout Test</div>
              </router-link>
            </li>

            <!-- Components - Always visible -->
            <li class="menu-item">
              <router-link to="/about" class="menu-item-button">
                <div class="icon">
                  <i class="icon-search-normal"></i>
                </div>
                <div class="text">About</div>
              </router-link>
            </li>

            <!-- Vuetify Demo - Always visible -->
            <li class="menu-item">
              <router-link to="/vuetify-demo" class="menu-item-button">
                <div class="icon">
                  <i class="icon-star"></i>
                </div>
                <div class="text">Vuetify Demo</div>
              </router-link>
            </li>
          </ul>
        </div>
      </div>
      <div class="bottom">
        <div class="image">
          <img src="@/assets/images/item/bot.png" alt="" />
        </div>
        <div class="content">
          <p class="f12-regular text-White">For more features</p>
          <p class="f12-bold text-White">Upgrade to Pro</p>
        </div>
      </div>
    </div>
  </div>
  <!-- /section-menu-left -->
  <!-- section-content-right -->
  <div class="section-content-right">
    <!-- header-dashboard -->
    <div class="header-dashboard">
      <div class="wrap">
        <div class="header-left">
          <div class="button-show-hide" @click="toggleSidebar">
            <i class="icon-menu"></i>
          </div>
          <h6>{{ pageTitle }}</h6>
          <form class="form-search flex-grow">
            <fieldset class="name">
              <input
                type="text"
                placeholder="Type to search …"
                class="show-search style-1"
                name="name"
                tabindex="2"
                value=""
                aria-required="true"
                required=""
              />
            </fieldset>
            <div class="button-submit">
              <button class="" type="submit"><i class="icon-search-normal1"></i></button>
            </div>
          </form>
        </div>
        <div class="header-grid">
          <div class="header-btn">
            <div class="popup-wrap message type-header">
              <div class="dropdown">
                <button
                  class="btn btn-secondary dropdown-toggle"
                  type="button"
                  id="dropdownMenuButton2"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span class="header-item">
                    <i class="icon-sms"></i>
                  </span>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-end has-content"
                  aria-labelledby="dropdownMenuButton1"
                >
                  <li>
                    <h6>Message</h6>
                  </li>
                  <li>
                    <div class="message-item w-full wg-user active">
                      <div class="image">
                        <img src="@/assets/images/avatar/user-1.png" alt="" />
                      </div>
                      <div class="flex-grow">
                        <div class="flex items-center justify-between">
                          <a href="#" class="body-title name">Cameron Williamson</a>
                          <div class="time">10:13 PM</div>
                        </div>
                        <div class="text-tiny desc">Hello?</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="message-item w-full wg-user active">
                      <div class="image">
                        <img src="@/assets/images/avatar/user-2.png" alt="" />
                      </div>
                      <div class="flex-grow">
                        <div class="flex items-center justify-between">
                          <a href="#" class="body-title name">Ralph Edwards</a>
                          <div class="time">10:13 PM</div>
                        </div>
                        <div class="text-tiny desc">Are you there? interested i this...</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="message-item w-full wg-user active">
                      <div class="image">
                        <img src="@/assets/images/avatar/user-3.png" alt="" />
                      </div>
                      <div class="flex-grow">
                        <div class="flex items-center justify-between">
                          <a href="#" class="body-title name">Eleanor Pena</a>
                          <div class="time">10:13 PM</div>
                        </div>
                        <div class="text-tiny desc">Interested in this loads?</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="message-item w-full wg-user active">
                      <div class="image">
                        <img src="@/assets/images/avatar/user-4.png" alt="" />
                      </div>
                      <div class="flex-grow">
                        <div class="flex items-center justify-between">
                          <a href="#" class="body-title name">Jane Cooper</a>
                          <div class="time">10:13 PM</div>
                        </div>
                        <div class="text-tiny desc">Okay...Do we have a deal?</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <a href="message.html" class="tf-button style-1 f12-bold w-100">
                      View All
                      <i class="icon icon-send"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="popup-wrap noti type-header">
              <div class="dropdown">
                <button
                  class="btn btn-secondary dropdown-toggle"
                  type="button"
                  id="dropdownMenuButton1"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span class="header-item">
                    <i class="icon-notification1"></i>
                  </span>
                </button>
                <ul
                  class="dropdown-menu dropdown-menu-end has-content"
                  aria-labelledby="dropdownMenuButton2"
                >
                  <li>
                    <h6>Notifications</h6>
                  </li>
                  <li>
                    <div class="notifications-item item-1">
                      <div class="image">
                        <i class="icon-setting-5"></i>
                      </div>
                      <div>
                        <div class="body-title-2">Discount available</div>
                        <div class="text-tiny">
                          Morbi sapien massa, ultricies at rhoncus at, ullamcorper nec diam
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="notifications-item item-2">
                      <div class="image">
                        <i class="icon-person"></i>
                      </div>
                      <div>
                        <div class="body-title-2">Account has been verified</div>
                        <div class="text-tiny">Mauris libero ex, iaculis vitae rhoncus et</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="notifications-item item-3">
                      <div class="image">
                        <i class="icon-message-text1"></i>
                      </div>
                      <div>
                        <div class="body-title-2">Order shipped successfully</div>
                        <div class="text-tiny">
                          Integer aliquam eros nec sollicitudin sollicitudin
                        </div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <div class="notifications-item item-4">
                      <div class="image">
                        <i class="icon-sms-tracking"></i>
                      </div>
                      <div>
                        <div class="body-title-2">Order pending: <span>ID 305830</span></div>
                        <div class="text-tiny">Ultricies at rhoncus at ullamcorper</div>
                      </div>
                    </div>
                  </li>
                  <li>
                    <a href="notifications.html" class="tf-button style-1 f12-bold w-100">
                      View All
                      <i class="icon icon-send"></i>
                    </a>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="line1"></div>
          <div class="popup-wrap user type-header">
            <div class="dropdown">
              <button
                class="btn btn-secondary dropdown-toggle"
                type="button"
                id="dropdownMenuButton3"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <span class="header-user wg-user">
                  <span class="image">
                    <img :src="userAvatar" :alt="userName" />
                  </span>
                  <span class="content flex flex-column">
                    <span class="label-02 text-Black name">{{ userName }}</span>
                    <span class="f14-regular text-Gray">{{ userRole }}</span>
                  </span>
                </span>
              </button>
              <ul
                class="dropdown-menu dropdown-menu-end has-content"
                aria-labelledby="dropdownMenuButton3"
              >
                <li>
                  <router-link to="/profile" class="user-item">
                    <div class="body-title-2">Profile</div>
                  </router-link>
                </li>
                <li>
                  <router-link to="/dashboard" class="user-item">
                    <div class="body-title-2">Dashboard</div>
                  </router-link>
                </li>
                <li>
                  <a href="#" class="user-item">
                    <div class="body-title-2">Inbox</div>
                    <div class="number">27</div>
                  </a>
                </li>
                <li>
                  <a href="transaction.html" class="user-item">
                    <div class="body-title-2">Transaction</div>
                  </a>
                </li>

                <!-- Settings - Only for users with settings access -->
                <CanAccess action="read" subject="Settings">
                  <li>
                    <a href="settings.html" class="user-item">
                      <div class="body-title-2">Settings</div>
                    </a>
                  </li>
                </CanAccess>

                <!-- Admin Panel - Only for admins/moderators -->
                <CanAccess action="read" subject="AdminPanel">
                  <li>
                    <router-link to="/admin" class="user-item">
                      <div class="body-title-2">Admin Panel</div>
                    </router-link>
                  </li>
                </CanAccess>

                <li>
                  <a href="crypto.html" class="user-item">
                    <div class="body-title-2">Crypto</div>
                  </a>
                </li>
                <li>
                  <a href="#" class="user-item" @click.prevent="handleLogout">
                    <div class="body-title-2">Log out</div>
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <!-- Theme Switcher Button (Default) -->
          <div class="divider"></div>
          <div class="setting cursor-pointer" @click="openThemeSwitcher">
            <i class="icon-setting1"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- inject page content here -->
    <slot />
  </div>

  <!-- Theme Switcher Component -->
  <Switcher />
</template>

<style scoped>
/* Theme Switcher Button Styles */
.divider {
  width: 1px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 12px;
}

.setting {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  cursor: pointer;
}

.setting:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.setting i {
  font-size: 18px;
  color: #6b7280;
}

.setting:hover i {
  color: #374151;
}

/* Dark theme support */
:global(body.dark-theme) .divider {
  background-color: #404040;
}

:global(body.dark-theme) .setting {
  background-color: rgba(255, 255, 255, 0.1);
}

:global(body.dark-theme) .setting:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

:global(body.dark-theme) .setting i {
  color: #e5e5e5;
}

:global(body.dark-theme) .setting:hover i {
  color: #ffffff;
}

/* Theme Switcher Tab Functionality */
:global(.widget-content-inner) {
  display: none;
}

:global(.widget-content-inner.active) {
  display: block;
}

:global(.widget-menu-tab .item-title) {
  cursor: pointer;
  padding: 10px 15px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

:global(.widget-menu-tab .item-title.active) {
  border-bottom-color: #2377FC;
  background-color: rgba(35, 119, 252, 0.1);
}

:global(.widget-menu-tab .item-title:hover) {
  background-color: rgba(0, 0, 0, 0.05);
}

:global(.offcanvas-body) {
  padding: 20px;
}

:global(.widget-tabs) {
  width: 100%;
}

:global(.widget-menu-tab) {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
}

:global(.widget-menu-tab .item-title) {
  flex: 1;
  text-align: center;
}

/* Color selection styling */
:global(.select-colors-theme) {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

:global(.select-colors-theme .item) {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

:global(.select-colors-theme .item.active) {
  border-color: #2377FC;
  transform: scale(1.1);
}

:global(.select-colors-theme .item:hover) {
  transform: scale(1.05);
}
</style>
