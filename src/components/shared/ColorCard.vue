<script setup>
import { computed } from 'vue'

// Props definition
const props = defineProps({
  // Card content
  walletName: {
    type: String,
    default: 'Main Wallet'
  },
  amount: {
    type: [String, Number],
    default: '$48,200.00'
  },
  cardNumber: {
    type: String,
    default: '6219 8610 2888 8075'
  },

  // Background styling
  background: {
    type: String,
    default: 'bg-1',
    validator: (value) => ['bg-1', 'bg-2', 'bg-3', 'bg-4'].includes(value)
  },

  // Customization
  cardClass: {
    type: String,
    default: ''
  },
  walletNameClass: {
    type: String,
    default: 'f12-medium text-GrayDark'
  },
  amountClass: {
    type: String,
    default: 'label-01'
  },
  cardNumberClass: {
    type: String,
    default: 'f12-medium'
  },

  // Icon customization
  showIcon: {
    type: Boolean,
    default: true
  },
  customIcon: {
    type: String,
    default: null
  },

  // Interaction
  clickable: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: <PERSON>olean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'click',
  'hover',
  'focus'
])

// Computed properties
const cardClasses = computed(() => {
  return [
    'my-card-item',
    props.background,
    props.cardClass,
    {
      'clickable': props.clickable,
      'disabled': props.disabled
    }
  ]
})

const formattedAmount = computed(() => {
  if (typeof props.amount === 'number') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(props.amount)
  }
  return props.amount
})

const maskedCardNumber = computed(() => {
  if (props.cardNumber && props.cardNumber.length > 4) {
    const lastFour = props.cardNumber.slice(-4)
    const masked = '**** **** **** ' + lastFour
    return masked
  }
  return props.cardNumber
})

// Event handlers
function handleClick(event) {
  if (!props.disabled && props.clickable) {
    emit('click', {
      walletName: props.walletName,
      amount: props.amount,
      cardNumber: props.cardNumber,
      background: props.background,
      event
    })
  }
}

function handleMouseEnter(event) {
  if (!props.disabled) {
    emit('hover', {
      type: 'enter',
      walletName: props.walletName,
      event
    })
  }
}

function handleMouseLeave(event) {
  if (!props.disabled) {
    emit('hover', {
      type: 'leave',
      walletName: props.walletName,
      event
    })
  }
}

function handleFocus(event) {
  if (!props.disabled) {
    emit('focus', {
      walletName: props.walletName,
      event
    })
  }
}
</script>

<template>
  <div
    :class="cardClasses"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @focus="handleFocus"
    :tabindex="clickable && !disabled ? 0 : -1"
    role="button"
    :aria-label="`${walletName} card with amount ${formattedAmount}`"
  >
    <!-- Card Content -->
    <div class="number">
      <!-- Wallet Name Slot -->
      <div v-if="$slots.walletName" class="wallet-name-slot">
        <slot name="walletName" :walletName="walletName" :background="background"></slot>
      </div>
      <div v-else :class="walletNameClass">{{ walletName }}</div>

      <!-- Amount Slot -->
      <div v-if="$slots.amount" class="amount-slot">
        <slot name="amount" :amount="amount" :formattedAmount="formattedAmount" :background="background"></slot>
      </div>
      <div v-else :class="amountClass">{{ formattedAmount }}</div>
    </div>

    <!-- Card Number -->
    <div class="bot">
      <!-- Card Number Slot -->
      <div v-if="$slots.cardNumber" class="card-number-slot">
        <slot name="cardNumber" :cardNumber="cardNumber" :maskedCardNumber="maskedCardNumber" :background="background"></slot>
      </div>
      <div v-else :class="cardNumberClass">{{ maskedCardNumber }}</div>

      <!-- Additional Content Slot -->
      <div v-if="$slots.additional" class="additional-content">
        <slot name="additional" :walletName="walletName" :amount="amount" :cardNumber="cardNumber" :background="background"></slot>
      </div>
    </div>

    <!-- Icon -->
    <div v-if="showIcon" class="icon">
      <!-- Custom Icon Slot -->
      <div v-if="$slots.icon">
        <slot name="icon" :background="background" :walletName="walletName"></slot>
      </div>
      <!-- Custom Icon HTML -->
      <div v-else-if="customIcon" v-html="customIcon"></div>
      <!-- Default Icon -->
      <svg
        v-else
        width="35"
        height="22"
        viewBox="0 0 35 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <ellipse cx="10.9373" cy="11" rx="10.9373" ry="11" fill="#161326" fill-opacity="0.67" />
        <ellipse cx="24.0627" cy="11" rx="10.9373" ry="11" fill="#161326" fill-opacity="0.7" />
      </svg>
    </div>
  </div>
</template>

<style scoped>
.my-card-item.clickable {
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.my-card-item.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.my-card-item.clickable:active {
  transform: translateY(0);
}

.my-card-item.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.my-card-item:focus {
  outline: 2px solid var(--Primary);
  outline-offset: 2px;
}

/* Slot styling */
.wallet-name-slot,
.amount-slot,
.card-number-slot,
.additional-content {
  width: 100%;
}
</style>
