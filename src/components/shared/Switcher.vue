<script setup>
import { onMounted, ref } from 'vue'

// Active tab state
const activeTab = ref(0)

// Active selections state
const activeSelections = ref({
  layoutWidth: 'full',
  menuStyle: 'default',
  menuPosition: 'fixed',
  headerPosition: 'fixed',
  loader: 'on',
  menuColor: '161326',
  headerColor: 'fff',
  primaryColor: '161326',
  backgroundColor: 'FFFFFF',
  themeMode: 'light'
})

// Switch tabs
const switchTab = (index) => {
  activeTab.value = index
}

// Theme settings functions
const applyLayoutWidth = (isBoxed) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isBoxed) {
      layoutWrap.classList.add('layout-width-boxed')
      activeSelections.value.layoutWidth = 'boxed'
      console.log('✅ Applied boxed layout - max-width: 1440px')
    } else {
      layoutWrap.classList.remove('layout-width-boxed')
      activeSelections.value.layoutWidth = 'full'
      console.log('✅ Applied full width layout')
    }
  }
}

const applyMenuStyle = (style) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    console.log(`Applying menu style: ${style}`)

    // Remove all menu style classes first
    layoutWrap.classList.remove('menu-style-icon', 'menu-style-icon-default')

    // Apply the selected style and update state
    if (style === 'icon-hover') {
      layoutWrap.classList.add('menu-style-icon')
      activeSelections.value.menuStyle = 'icon-hover'
      console.log('✅ Applied ICON HOVER menu style - 75px width, EXPANDS to 256px on hover')
    } else if (style === 'icon-default') {
      layoutWrap.classList.add('menu-style-icon-default')
      activeSelections.value.menuStyle = 'icon-default'
      console.log('✅ Applied ICON DEFAULT menu style - 75px width, STAYS collapsed (no hover expansion)')
    } else {
      activeSelections.value.menuStyle = 'default'
      console.log('✅ Applied DEFAULT menu style - 256px width, full sidebar always visible')
    }

    // Debug: Show current classes
    console.log('Current layout classes:', layoutWrap.className)
  } else {
    console.error('❌ Layout wrap element not found')
  }
}

const applyMenuPosition = (isScrollable) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isScrollable) {
      layoutWrap.classList.add('menu-position-scrollable')
      console.log('Applied scrollable menu position')
    } else {
      layoutWrap.classList.remove('menu-position-scrollable')
      console.log('Applied fixed menu position')
    }
  }
}

const applyHeaderPosition = (isScrollable) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isScrollable) {
      layoutWrap.classList.add('header-position-scrollable')
      console.log('Applied scrollable header position')
    } else {
      layoutWrap.classList.remove('header-position-scrollable')
      console.log('Applied fixed header position')
    }
  }
}

const applyLoader = (isEnabled) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    if (isEnabled) {
      layoutWrap.classList.remove('loader-off')
      console.log('Enabled loader')
    } else {
      layoutWrap.classList.add('loader-off')
      console.log('Disabled loader')
    }
  }
}

// Color theme functions
const applyMenuColor = (color) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  const logoHeader = document.querySelector('#logo_header')

  if (layoutWrap) {
    layoutWrap.setAttribute('data-menu-background', `colors-menu-${color}`)
    activeSelections.value.menuColor = color
    console.log(`✅ Applied menu color: ${color}`)

    // Update logo based on color
    if (logoHeader) {
      const lightLogo = logoHeader.getAttribute('data-light')
      const darkLogo = logoHeader.getAttribute('data-dark')

      if (color === 'fff') {
        logoHeader.src = darkLogo || logoHeader.src
      } else {
        logoHeader.src = lightLogo || logoHeader.src
      }
    }
  }
}

const applyHeaderColor = (color) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    layoutWrap.setAttribute('data-colors-header', `colors-header-${color}`)
    console.log(`Applied header color: ${color}`)
  }
}

const applyPrimaryColor = (color) => {
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    layoutWrap.setAttribute('data-theme-primary', `theme-primary-${color}`)
    console.log(`Applied primary color: ${color}`)
  }
}

const applyBackgroundColor = (color) => {
  const body = document.body
  if (body) {
    body.setAttribute('data-theme-background', `theme-background-${color}`)
    console.log(`Applied background color: ${color}`)
  }
}

// Dark/Light theme functions
const applyDarkTheme = () => {
  const body = document.body
  body.classList.add('dark-theme')
  localStorage.setItem('toggled', 'dark-theme')
  activeSelections.value.themeMode = 'dark'
  console.log('✅ Applied dark theme')
}

const applyLightTheme = () => {
  const body = document.body
  body.classList.remove('dark-theme')
  localStorage.setItem('toggled', 'light-theme')
  activeSelections.value.themeMode = 'light'
  console.log('✅ Applied light theme')
}

// Clear all settings
const clearAllStyles = () => {
  const layoutWrap = document.querySelector('.layout-wrap')
  const body = document.body

  if (layoutWrap) {
    // Remove layout classes
    layoutWrap.classList.remove(
      'layout-width-boxed',
      'menu-style-icon',
      'menu-style-icon-default',
      'menu-position-scrollable',
      'header-position-scrollable',
      'loader-off'
    )

    // Remove color attributes
    layoutWrap.removeAttribute('data-menu-background')
    layoutWrap.removeAttribute('data-colors-header')
    layoutWrap.removeAttribute('data-theme-primary')
  }

  if (body) {
    body.removeAttribute('data-theme-background')
    body.classList.remove('dark-theme')
  }

  // Reset localStorage and active selections
  localStorage.setItem('toggled', 'light-theme')

  // Reset active selections to defaults
  activeSelections.value = {
    layoutWidth: 'full',
    menuStyle: 'default',
    menuPosition: 'fixed',
    headerPosition: 'fixed',
    loader: 'on',
    menuColor: '161326',
    headerColor: 'fff',
    primaryColor: '161326',
    backgroundColor: 'FFFFFF'
  }

  console.log('✅ All styles cleared and reset to defaults')
}

const clearAllColors = () => {
  const layoutWrap = document.querySelector('.layout-wrap')
  const body = document.body

  if (layoutWrap) {
    layoutWrap.removeAttribute('data-menu-background')
    layoutWrap.removeAttribute('data-colors-header')
    layoutWrap.removeAttribute('data-theme-primary')
  }

  if (body) {
    body.removeAttribute('data-theme-background')
  }

  console.log('All colors cleared')
}

// Debug function
const debugCurrentState = () => {
  const layoutWrap = document.querySelector('.layout-wrap')
  console.log('=== SWITCHER DEBUG ===')
  console.log('Layout wrap element:', layoutWrap)
  if (layoutWrap) {
    console.log('Layout wrap classes:', layoutWrap.className)
    console.log('Has menu-style-icon:', layoutWrap.classList.contains('menu-style-icon'))
    console.log('Has menu-style-icon-default:', layoutWrap.classList.contains('menu-style-icon-default'))
    console.log('Has layout-width-boxed:', layoutWrap.classList.contains('layout-width-boxed'))
  }
  console.log('Active selections:', activeSelections.value)
  console.log('===================')
}

onMounted(() => {
  console.log('Switcher component mounted')

  // Clean up any existing offcanvas elements from switcher.js
  const existingOffcanvas = document.querySelectorAll('#offcanvasRight')
  existingOffcanvas.forEach((element, index) => {
    if (index > 0) { // Keep only the first one (our Vue component)
      console.log('Removing duplicate offcanvas element from switcher.js')
      element.remove()
    }
  })

  // Remove any conflicting CSS classes or styles that might be applied by switcher.js
  const layoutWrap = document.querySelector('.layout-wrap')
  if (layoutWrap) {
    // Clean up any inline styles that might have been applied
    layoutWrap.style.removeProperty('width')
    layoutWrap.style.removeProperty('background-color')
    console.log('Cleaned up any conflicting inline styles')
  }

  // Initialize theme state from localStorage
  const savedTheme = localStorage.getItem('toggled')
  if (savedTheme === 'dark-theme') {
    document.body.classList.add('dark-theme')
    activeSelections.value.themeMode = 'dark'
    console.log('Dark theme initialized from localStorage')
  } else {
    document.body.classList.remove('dark-theme')
    activeSelections.value.themeMode = 'light'
    console.log('Light theme initialized (default or from localStorage)')
  }

  // Debug initial state
  setTimeout(() => {
    debugCurrentState()
  }, 1000)
})
</script>

<template>
  <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRight">
    <div class="offcanvas-header">
      <h6 id="offcanvasRightLabel">Setting</h6>
      <button
        type="button"
        class="btn-close text-reset"
        data-bs-dismiss="offcanvas"
        aria-label="Close"
      ></button>
    </div>

    <div class="offcanvas-body">
      <div class="widget-tabs">
        <ul class="widget-menu-tab style-1">
          <li
            :class="['item-title', { active: activeTab === 0 }]"
            @click="switchTab(0)"
          >
            <span class="inner">
              <div class="body-title">Theme Style</div>
            </span>
          </li>

          <li
            :class="['item-title', { active: activeTab === 1 }]"
            @click="switchTab(1)"
          >
            <span class="inner">
              <div class="body-title">Theme Colors</div>
            </span>
          </li>
        </ul>

        <div class="widget-content-tab">
          <div :class="['widget-content-inner', { active: activeTab === 0 }]">
            <form class="form-theme-style">
              <fieldset class="layout-width">
                <div class="body-title mb-5">Layout width style</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="boxed"
                      type="radio"
                      name="width-style"
                      id="width-style2"
                      :checked="activeSelections.layoutWidth === 'boxed'"
                      @change="applyLayoutWidth(true)"
                    />
                    <label for="width-style2" class="">
                      <div class="body-title">Boxed</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="full"
                      type="radio"
                      name="width-style"
                      id="width-style1"
                      :checked="activeSelections.layoutWidth === 'full'"
                      @change="applyLayoutWidth(false)"
                    />
                    <label for="width-style1" class="">
                      <div class="body-title">Full width</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="menu-style">
                <div class="body-title mb-5">Vertical & Horizontal menu style</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="menu-click"
                      type="radio"
                      name="menu-style"
                      id="menu-style1"
                      :checked="activeSelections.menuStyle === 'default'"
                      @change="applyMenuStyle('default')"
                    />
                    <label class="" for="menu-style1">
                      <div class="body-title">Menu click</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="icon-hover"
                      type="radio"
                      name="menu-style"
                      id="menu-style2"
                      :checked="activeSelections.menuStyle === 'icon-hover'"
                      @change="applyMenuStyle('icon-hover')"
                    />
                    <label class="" for="menu-style2">
                      <div class="body-title">Icon hover</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="icon-default"
                      type="radio"
                      name="menu-style"
                      id="menu-style3"
                      :checked="activeSelections.menuStyle === 'icon-default'"
                      @change="applyMenuStyle('icon-default')"
                    />
                    <label class="" for="menu-style3">
                      <div class="body-title">Icon default</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="menu-position">
                <div class="body-title mb-5">Menu position</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="menu-fixed"
                      type="radio"
                      name="menu-position"
                      id="menu-position1"
                      @change="applyMenuPosition(false)"
                      checked
                    />
                    <label class="" for="menu-position1">
                      <div class="body-title">Fixed</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="menu-scrollable"
                      type="radio"
                      name="menu-position"
                      id="menu-position2"
                      @change="applyMenuPosition(true)"
                    />
                    <label class="" for="menu-position2">
                      <div class="body-title">Scrollable</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="header-position">
                <div class="body-title mb-5">Header positions</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="header-fixed"
                      type="radio"
                      name="header-positions"
                      id="header-positions1"
                      @change="applyHeaderPosition(false)"
                      checked
                    />
                    <label class="" for="header-positions1">
                      <div class="body-title">Fixed</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="header-scrollable"
                      type="radio"
                      name="header-positions"
                      id="header-positions2"
                      @change="applyHeaderPosition(true)"
                    />
                    <label class="" for="header-positions2">
                      <div class="body-title">Scrollable</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="style-loader">
                <div class="body-title mb-5">Loader</div>

                <div class="radio-buttons">
                  <div class="item">
                    <input
                      class="style-loader-on"
                      type="radio"
                      name="loader"
                      id="loader1"
                      @change="applyLoader(true)"
                      checked
                    />
                    <label class="" for="loader1">
                      <div class="body-title">Enable</div>
                    </label>
                  </div>

                  <div class="item">
                    <input
                      class="style-loader-off"
                      type="radio"
                      name="loader"
                      id="loader2"
                      @change="applyLoader(false)"
                    />
                    <label class="" for="loader2">
                      <div class="body-title">Disable</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <fieldset class="theme-dark-light">
                <div class="body-title mb-5">Theme Mode</div>

                <div class="radio-buttons">
                  <div class="item light">
                    <input
                      type="radio"
                      name="theme-mode"
                      id="theme-light"
                      :checked="activeSelections.themeMode === 'light'"
                      @change="applyLightTheme"
                    />
                    <label class="" for="theme-light">
                      <div class="body-title">Light</div>
                    </label>
                  </div>

                  <div class="item dark">
                    <input
                      type="radio"
                      name="theme-mode"
                      id="theme-dark"
                      :checked="activeSelections.themeMode === 'dark'"
                      @change="applyDarkTheme"
                    />
                    <label class="" for="theme-dark">
                      <div class="body-title">Dark</div>
                    </label>
                  </div>
                </div>
              </fieldset>

              <div
                class="tf-button style-1 label-01 w-100 cursor-pointer w-full button-clear-select"
                @click="clearAllStyles"
              >
                Clear all
              </div>

              <!-- Debug Button -->
              <div
                class="tf-button style-2 label-01 w-100 cursor-pointer w-full mt-2"
                @click="debugCurrentState"
                style="background: #e3f2fd; color: #1976d2; border: 1px solid #2196f3;"
              >
                Debug State
              </div>
            </form>
          </div>

          <div :class="['widget-content-inner', { active: activeTab === 1 }]">
            <form class="form-theme-color">
              <fieldset class="menu-color">
                <div class="body-title mb-10">Menu Background color</div>

                <div class="select-colors-theme colors-menu mb-10">
                  <div
                    :class="['item', 'color-161326', 'default', { active: activeSelections.menuColor === '161326' }]"
                    @click="applyMenuColor('161326')"
                  ></div>

                  <div
                    :class="['item', 'color-1E293B', { active: activeSelections.menuColor === '1E293B' }]"
                    @click="applyMenuColor('1E293B')"
                  ></div>

                  <div
                    :class="['item', 'color-fff', { active: activeSelections.menuColor === 'fff' }]"
                    @click="applyMenuColor('fff')"
                  ></div>

                  <div
                    :class="['item', 'color-3A3043', { active: activeSelections.menuColor === '3A3043' }]"
                    @click="applyMenuColor('3A3043')"
                  ></div>
                </div>

                <div class="text-tiny">
                  Note:If you want to change color Menu dynamically change from below Theme Primary
                  color picker
                </div>
              </fieldset>

              <fieldset class="">
                <div class="body-title mb-10">Header Background color</div>

                <div class="select-colors-theme colors-header mb-10">
                  <div
                    class="item color-fff active default"
                    @click="applyHeaderColor('fff')"
                  ></div>

                  <div
                    class="item color-1E293B"
                    @click="applyHeaderColor('1E293B')"
                  ></div>

                  <div
                    class="item color-161326"
                    @click="applyHeaderColor('161326')"
                  ></div>

                  <div
                    class="item color-3A3043"
                    @click="applyHeaderColor('3A3043')"
                  ></div>
                </div>

                <div class="text-tiny">
                  Note:If you want to change color Header dynamically change from below Theme
                  Primary color picker
                </div>
              </fieldset>

              <fieldset class="">
                <div class="body-title mb-10">Theme Primary color</div>

                <div class="select-colors-theme colors-theme-primary mb-10">
                  <div
                    class="item color-161326 active default"
                    @click="applyPrimaryColor('161326')"
                  ></div>

                  <div
                    class="item color-2377FC"
                    @click="applyPrimaryColor('2377FC')"
                  ></div>

                  <div
                    class="item color-35988D"
                    @click="applyPrimaryColor('35988D')"
                  ></div>

                  <div
                    class="item color-7047D6"
                    @click="applyPrimaryColor('7047D6')"
                  ></div>
                </div>
              </fieldset>

              <fieldset class="">
                <div class="body-title mb-10">Theme Background color</div>

                <div class="select-colors-theme colors-theme-background mb-10">
                  <div
                    class="item color-FFFFFF active default"
                    @click="applyBackgroundColor('FFFFFF')"
                  ></div>

                  <div
                    class="item color-252E3A"
                    @click="applyBackgroundColor('252E3A')"
                  ></div>

                  <div
                    class="item color-1E1D2A"
                    @click="applyBackgroundColor('1E1D2A')"
                  ></div>

                  <div
                    class="item color-1B2627"
                    @click="applyBackgroundColor('1B2627')"
                  ></div>
                </div>
              </fieldset>
              <div
                class="tf-button style-1 label-01 w-100 cursor-pointer w-full button-clear-select"
                @click="clearAllColors"
              >
                Clear all
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* Switcher Component Styling */

/* Tab styling */
.widget-menu-tab {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 20px;
}

.widget-menu-tab .item-title {
  flex: 1;
  text-align: center;
  cursor: pointer;
  padding: 12px 16px;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
}

.widget-menu-tab .item-title.active {
  border-bottom-color: #2377FC;
  background-color: rgba(35, 119, 252, 0.1);
}

.widget-menu-tab .item-title:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Content panels */
.widget-content-inner {
  display: none;
}

.widget-content-inner.active {
  display: block;
}

/* Form styling */
.form-theme-style,
.form-theme-color {
  padding: 20px 0;
}

fieldset {
  border: none;
  margin-bottom: 24px;
  padding: 0;
}

.body-title {
  font-weight: 600;
  font-size: 14px;
  color: #374151;
  margin-bottom: 8px;
}

/* Radio buttons styling */
.radio-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.radio-buttons .item {
  flex: 1;
  min-width: 120px;
}

.radio-buttons input[type="radio"] {
  margin-right: 8px;
}

.radio-buttons label {
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.radio-buttons label:hover {
  border-color: #2377FC;
  background-color: rgba(35, 119, 252, 0.05);
}

.radio-buttons input[type="radio"]:checked + label {
  border-color: #2377FC;
  background-color: rgba(35, 119, 252, 0.1);
  color: #2377FC;
}

/* Color selection styling */
.select-colors-theme {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.select-colors-theme .item {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.select-colors-theme .item:hover {
  transform: scale(1.1);
}

.select-colors-theme .item.active {
  border-color: #2377FC;
  transform: scale(1.15);
}

.select-colors-theme .item.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0,0,0,0.5);
}

/* Color definitions */
.color-161326 { background-color: #161326; }
.color-1E293B { background-color: #1E293B; }
.color-fff { background-color: #ffffff; border: 1px solid #e5e7eb; }
.color-3A3043 { background-color: #3A3043; }
.color-2377FC { background-color: #2377FC; }
.color-35988D { background-color: #35988D; }
.color-7047D6 { background-color: #7047D6; }
.color-FFFFFF { background-color: #FFFFFF; border: 1px solid #e5e7eb; }
.color-252E3A { background-color: #252E3A; }
.color-1E1D2A { background-color: #1E1D2A; }
.color-1B2627 { background-color: #1B2627; }

/* Button styling */
.tf-button {
  padding: 12px 24px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.tf-button.style-1 {
  background-color: #2377FC;
  color: white;
}

.tf-button.style-1:hover {
  background-color: #1d63d1;
  transform: translateY(-1px);
}

.tf-button.style-2 {
  background-color: #e3f2fd;
  color: #1976d2;
  border: 1px solid #2196f3;
}

.tf-button.style-2:hover {
  background-color: #bbdefb;
}

/* Utility classes */
.mb-5 { margin-bottom: 5px; }
.mb-10 { margin-bottom: 10px; }
.mt-2 { margin-top: 8px; }
.w-100 { width: 100%; }
.cursor-pointer { cursor: pointer; }

/* Dark theme support - Comprehensive styling */
:global(body.dark-theme) .offcanvas {
  background-color: #1a1a1a;
  color: #e5e5e5;
}

:global(body.dark-theme) .offcanvas-header {
  border-bottom-color: #404040;
}

:global(body.dark-theme) .offcanvas-title {
  color: #e5e5e5;
}

:global(body.dark-theme) .body-title {
  color: #e5e5e5;
}

:global(body.dark-theme) .widget-menu-tab {
  border-bottom-color: #404040;
  background-color: #2a2a2a;
}

:global(body.dark-theme) .widget-menu-tab .item-title {
  color: #b0b0b0;
  border-bottom-color: transparent;
}

:global(body.dark-theme) .widget-menu-tab .item-title.active {
  color: #2377FC;
  background-color: rgba(35, 119, 252, 0.2);
  border-bottom-color: #2377FC;
}

:global(body.dark-theme) .widget-menu-tab .item-title:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #e5e5e5;
}

:global(body.dark-theme) .widget-content-inner {
  background-color: #1a1a1a;
}

:global(body.dark-theme) .form-theme-style,
:global(body.dark-theme) .form-theme-color {
  background-color: #1a1a1a;
}

:global(body.dark-theme) .radio-buttons label {
  border-color: #404040;
  background-color: #2a2a2a;
  color: #e5e5e5;
}

:global(body.dark-theme) .radio-buttons label:hover {
  border-color: #2377FC;
  background-color: rgba(35, 119, 252, 0.2);
  color: #ffffff;
}

:global(body.dark-theme) .radio-buttons input[type="radio"]:checked + label {
  border-color: #2377FC;
  background-color: rgba(35, 119, 252, 0.3);
  color: #2377FC;
}

:global(body.dark-theme) .tf-button.style-1 {
  background-color: #2377FC;
  color: white;
}

:global(body.dark-theme) .tf-button.style-1:hover {
  background-color: #1d63d1;
}

:global(body.dark-theme) .tf-button.style-2 {
  background-color: rgba(35, 119, 252, 0.2);
  color: #2377FC;
  border-color: #2377FC;
}

:global(body.dark-theme) .tf-button.style-2:hover {
  background-color: rgba(35, 119, 252, 0.3);
}

/* Dark theme offcanvas backdrop */
:global(body.dark-theme) .offcanvas-backdrop {
  background-color: rgba(0, 0, 0, 0.7);
}

/* Dark theme close button */
:global(body.dark-theme) .btn-close {
  filter: invert(1);
}
</style>
