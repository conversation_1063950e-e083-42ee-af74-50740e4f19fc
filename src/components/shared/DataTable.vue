<script setup>
import { ref, computed, watch } from 'vue'

// Props definition
const props = defineProps({
  // Data and columns
  data: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    required: true
  },

  // Table configuration
  title: {
    type: String,
    default: 'Data Table'
  },
  searchable: {
    type: Boolean,
    default: true
  },
  sortable: {
    type: Boolean,
    default: true
  },
  selectable: {
    type: Boolean,
    default: true
  },

  // Pagination
  pagination: {
    type: Boolean,
    default: true
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },

  // Styling
  tableClass: {
    type: String,
    default: ''
  },

  // Actions
  showActions: {
    type: <PERSON>olean,
    default: true
  },
  showFilters: {
    type: Boolean,
    default: true
  },
  showExport: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'row-select',
  'row-click',
  'sort-change',
  'search',
  'filter-change',
  'export-data'
])

// Reactive state
const searchQuery = ref('')
const sortColumn = ref('')
const sortDirection = ref('asc')
const selectedRows = ref(new Set())
const currentPage = ref(1)
const filterValues = ref({})

// Computed properties
const filteredData = computed(() => {
  let result = [...props.data]

  // Apply search filter
  if (searchQuery.value && props.searchable) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(row => {
      return props.columns.some(column => {
        const value = getNestedValue(row, column.key)
        return String(value).toLowerCase().includes(query)
      })
    })
  }

  // Apply column filters
  Object.entries(filterValues.value).forEach(([key, value]) => {
    if (value) {
      result = result.filter(row => {
        const rowValue = getNestedValue(row, key)
        return String(rowValue).toLowerCase().includes(String(value).toLowerCase())
      })
    }
  })

  return result
})

const sortedData = computed(() => {
  if (!sortColumn.value || !props.sortable) {
    return filteredData.value
  }

  return [...filteredData.value].sort((a, b) => {
    const aValue = getNestedValue(a, sortColumn.value)
    const bValue = getNestedValue(b, sortColumn.value)

    let comparison = 0
    if (aValue > bValue) comparison = 1
    if (aValue < bValue) comparison = -1

    return sortDirection.value === 'desc' ? -comparison : comparison
  })
})

const paginatedData = computed(() => {
  if (!props.pagination) {
    return sortedData.value
  }

  const start = (currentPage.value - 1) * props.itemsPerPage
  const end = start + props.itemsPerPage
  return sortedData.value.slice(start, end)
})

const totalPages = computed(() => {
  if (!props.pagination) return 1
  return Math.ceil(sortedData.value.length / props.itemsPerPage)
})

const isAllSelected = computed(() => {
  return paginatedData.value.length > 0 &&
         paginatedData.value.every(row => selectedRows.value.has(getRowId(row)))
})

// Helper functions
function getNestedValue(obj, path) {
  return path.split('.').reduce((current, key) => current?.[key], obj) || ''
}

function getRowId(row) {
  return row.id || JSON.stringify(row)
}

function handleSort(column) {
  if (!props.sortable || !column.sortable) return

  if (sortColumn.value === column.key) {
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    sortColumn.value = column.key
    sortDirection.value = 'asc'
  }

  emit('sort-change', {
    column: column.key,
    direction: sortDirection.value
  })
}

function handleRowSelect(row) {
  const rowId = getRowId(row)
  if (selectedRows.value.has(rowId)) {
    selectedRows.value.delete(rowId)
  } else {
    selectedRows.value.add(rowId)
  }

  emit('row-select', {
    row,
    selected: selectedRows.value.has(rowId),
    selectedRows: Array.from(selectedRows.value)
  })
}

function handleSelectAll() {
  if (isAllSelected.value) {
    paginatedData.value.forEach(row => {
      selectedRows.value.delete(getRowId(row))
    })
  } else {
    paginatedData.value.forEach(row => {
      selectedRows.value.add(getRowId(row))
    })
  }

  emit('row-select', {
    selectAll: true,
    selected: !isAllSelected.value,
    selectedRows: Array.from(selectedRows.value)
  })
}

function handleSearch() {
  currentPage.value = 1
  emit('search', searchQuery.value)
}

function handleRowClick(row) {
  emit('row-click', row)
}

function handleExport() {
  emit('export-data', {
    data: sortedData.value,
    columns: props.columns
  })
}

// Additional computed properties
const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(1, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible - 1)

  if (end - start + 1 < maxVisible) {
    start = Math.max(1, end - maxVisible + 1)
  }

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// Helper functions for formatting and styling
function formatDate(date, format = 'default') {
  if (!date) return ''

  const dateObj = new Date(date)
  if (isNaN(dateObj.getTime())) return date

  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString()
    case 'long':
      return dateObj.toLocaleDateString() + ' ' + dateObj.toLocaleTimeString()
    case 'time':
      return dateObj.toLocaleTimeString()
    default:
      return dateObj.toLocaleDateString() + '<br><span class="f12-medium">' +
             dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) + '</span>'
  }
}

function formatCurrency(amount, currency = 'USD') {
  if (!amount && amount !== 0) return ''

  const formatter = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: currency
  })

  return formatter.format(amount)
}

function getStatusClass(status) {
  if (!status) return ''

  const statusLower = status.toLowerCase()
  switch (statusLower) {
    case 'completed':
    case 'success':
    case 'active':
      return 'bg-YellowGreen'
    case 'pending':
    case 'processing':
      return 'bg-LightGray'
    case 'canceled':
    case 'cancelled':
    case 'failed':
    case 'error':
      return 'bg-LightGray type-red'
    default:
      return 'bg-LightGray'
  }
}

function getStatusIcon(status) {
  if (!status) return ''

  const statusLower = status.toLowerCase()
  switch (statusLower) {
    case 'completed':
    case 'success':
    case 'active':
      return 'icon icon-check'
    default:
      return ''
  }
}

// Watch for search changes
watch(searchQuery, () => {
  handleSearch()
})

// Watch for items per page changes
watch(() => props.itemsPerPage, (newValue) => {
  currentPage.value = 1
})

// Watch for data changes
watch(() => props.data, () => {
  currentPage.value = 1
  selectedRows.value.clear()
}, { deep: true })
</script>

<template>
  <div class="main-content-wrap">
    <div class="tf-container">
      <!-- Search and Actions Bar -->
      <div class="topbar-search" v-if="searchable || showActions || showFilters">
        <!-- Search Form -->
        <form class="form-search flex-grow" @submit.prevent="handleSearch" v-if="searchable">
          <fieldset class="name">
            <input
              type="text"
              placeholder="Search..."
              class="show-search style-1"
              name="search"
              tabindex="2"
              v-model="searchQuery"
              aria-required="true"
            />
          </fieldset>
          <div class="button-submit">
            <button type="submit"><i class="icon-search-normal1"></i></button>
          </div>
        </form>

        <!-- Action Buttons -->
        <div class="right" v-if="showActions || showFilters">
          <!-- Export Button -->
          <a
            href="#"
            class="tf-button style-2 f12-bold d-md-flex d-none"
            @click.prevent="handleExport"
            v-if="showExport"
          >
            <i class="icon icon-receive-square"></i>
            Export Data
          </a>

          <!-- Sort Filter -->
          <div class="dropdown default style-fill" v-if="showFilters">
            <button
              class="btn btn-secondary dropdown-toggle"
              type="button"
              data-bs-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
            >
              <i class="icon icon-setting-5"></i>
              Sort
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
              <li v-for="column in columns.filter(col => col.sortable !== false)" :key="column.key">
                <a
                  href="javascript:void(0);"
                  @click="handleSort(column)"
                  :class="{ active: sortColumn === column.key }"
                >
                  {{ column.label }}
                  <span v-if="sortColumn === column.key">
                    {{ sortDirection === 'asc' ? '↑' : '↓' }}
                  </span>
                </a>
              </li>
            </ul>
          </div>

          <!-- Items per page selector -->
          <select
            class="image-select center d-md-flex d-none"
            v-model="itemsPerPage"
            v-if="pagination"
          >
            <option value="5">5 per page</option>
            <option value="10">10 per page</option>
            <option value="25">25 per page</option>
            <option value="50">50 per page</option>
          </select>

          <!-- Additional Actions Slot -->
          <div v-if="$slots.actions">
            <slot name="actions"></slot>
          </div>
        </div>
      </div>
      <!-- Data Table -->
      <div class="table-list-transaction" :class="tableClass">
        <!-- Table Header -->
        <div class="list-transaction-head">
          <!-- Select All Checkbox -->
          <div v-if="selectable">
            <div class="tf-checkbox-wrapp">
              <input
                type="checkbox"
                :checked="isAllSelected"
                @change="handleSelectAll"
                class="checkbox-item"
              />
              <div>
                <i class="icon-check"></i>
              </div>
            </div>
            <div class="f12-bold text-White">Select</div>
          </div>

          <!-- Dynamic Column Headers -->
          <div
            v-for="column in columns"
            :key="column.key"
            @click="handleSort(column)"
            :class="{
              'sortable': sortable && column.sortable !== false,
              'sorted': sortColumn === column.key
            }"
            style="cursor: pointer;"
          >
            <div class="f12-bold text-White">{{ column.label }}</div>
            <!-- Sort Icon -->
            <svg
              v-if="sortable && column.sortable !== false"
              width="14"
              height="14"
              viewBox="0 0 14 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              :class="{
                'sort-active': sortColumn === column.key,
                'sort-desc': sortColumn === column.key && sortDirection === 'desc'
              }"
            >
              <path
                d="M6.53338 3.9199C6.53338 4.03073 6.49253 4.14158 6.40503 4.22908C6.23587 4.39825 5.95587 4.39825 5.7867 4.22908L3.92587 2.36825L2.06503 4.22908C1.89586 4.39825 1.61587 4.39825 1.4467 4.22908C1.27753 4.05992 1.27753 3.77992 1.4467 3.61075L3.61672 1.44073C3.69838 1.35906 3.8092 1.31242 3.92587 1.31242C4.04253 1.31242 4.15338 1.35906 4.23505 1.44073L6.40503 3.61075C6.4867 3.69825 6.53338 3.80906 6.53338 3.9199Z"
                fill="white"
              />
              <path
                d="M4.36328 1.75L4.36328 12.25C4.36328 12.4892 4.16495 12.6875 3.92578 12.6875C3.68661 12.6875 3.48828 12.4892 3.48828 12.25L3.48828 1.75C3.48828 1.51083 3.68661 1.3125 3.92578 1.3125C4.16495 1.3125 4.36328 1.51083 4.36328 1.75Z"
                fill="white"
              />
              <path
                d="M12.6876 10.08C12.6876 10.1909 12.6468 10.3017 12.5593 10.3892L10.3893 12.5592C10.3076 12.6409 10.1968 12.6875 10.0802 12.6875C9.9635 12.6875 9.85265 12.6409 9.77098 12.5592L7.601 10.3892C7.43183 10.22 7.43183 9.94 7.601 9.77083C7.77016 9.60167 8.05016 9.60167 8.21933 9.77083L10.0802 11.6317L11.941 9.77083C12.1101 9.60167 12.3902 9.60167 12.5593 9.77083C12.6468 9.8525 12.6876 9.96335 12.6876 10.08Z"
                fill="white"
              />
              <path
                d="M10.5117 1.75L10.5117 12.25C10.5117 12.4892 10.3134 12.6875 10.0742 12.6875C9.83505 12.6875 9.63672 12.4892 9.63672 12.25L9.63672 1.75C9.63672 1.51083 9.83505 1.3125 10.0742 1.3125C10.3134 1.3125 10.5117 1.51083 10.5117 1.75Z"
                fill="white"
              />
            </svg>
          </div>
        </div>
        <!-- Table Body -->
        <table class="list-transaction-content w-100">
          <tbody>
            <!-- Dynamic Rows -->
            <tr
              v-for="(row, index) in paginatedData"
              :key="getRowId(row)"
              class="tf-table-item"
              :class="{
                'checked': selectedRows.has(getRowId(row))
              }"
              @click="handleRowClick(row)"
            >
              <!-- Selection Checkbox -->
              <td v-if="selectable">
                <div class="tf-cart-checkbox">
                  <div class="tf-checkbox-wrapp">
                    <input
                      class="checkbox-item"
                      type="checkbox"
                      :checked="selectedRows.has(getRowId(row))"
                      @change="handleRowSelect(row)"
                      @click.stop
                    />
                    <div>
                      <i class="icon-check"></i>
                    </div>
                  </div>
                </div>
              </td>

              <!-- Dynamic Columns -->
              <td v-for="column in columns" :key="column.key">
                <!-- Custom slot for column -->
                <div v-if="$slots[`column-${column.key}`]">
                  <slot
                    :name="`column-${column.key}`"
                    :row="row"
                    :value="getNestedValue(row, column.key)"
                    :index="index"
                  ></slot>
                </div>

                <!-- Image column type -->
                <div v-else-if="column.type === 'image'" class="wrap-image" :class="column.imageClass">
                  <div class="image" v-if="getNestedValue(row, column.imageKey || column.key)">
                    <img :src="getNestedValue(row, column.imageKey || column.key)" :alt="column.label" />
                  </div>
                  <div class="f12-medium" v-if="column.textKey">
                    {{ getNestedValue(row, column.textKey) }}
                  </div>
                  <div class="f12-bold" v-else>
                    {{ getNestedValue(row, column.key) }}
                  </div>
                </div>

                <!-- Status column type -->
                <div v-else-if="column.type === 'status'" class="box-status" :class="getStatusClass(getNestedValue(row, column.key))">
                  <i v-if="getStatusIcon(getNestedValue(row, column.key))" :class="getStatusIcon(getNestedValue(row, column.key))"></i>
                  <span class="font-poppins">{{ getNestedValue(row, column.key) }}</span>
                </div>

                <!-- Date column type -->
                <div v-else-if="column.type === 'date'" class="f12-bold">
                  {{ formatDate(getNestedValue(row, column.key), column.format) }}
                </div>

                <!-- Currency column type -->
                <div v-else-if="column.type === 'currency'" class="f12-medium" :data-title="`${column.label}: `">
                  {{ formatCurrency(getNestedValue(row, column.key), column.currency) }}
                </div>

                <!-- Default text column -->
                <div v-else class="f12-medium" :class="column.cellClass" :data-title="`${column.label}: `">
                  {{ getNestedValue(row, column.key) }}
                </div>
              </td>
            </tr>

            <!-- Empty State -->
            <tr v-if="paginatedData.length === 0" class="tf-table-item">
              <td :colspan="columns.length + (selectable ? 1 : 0)" class="text-center">
                <div class="f12-medium text-muted py-4">
                  <slot name="empty-state">
                    No data available
                  </slot>
                </div>
              </td>
            </tr>
          </tbody>
        </table>


        <!-- Pagination -->
        <div class="pagination-wrapper mt-4" v-if="pagination && totalPages > 1">
          <div class="d-flex justify-content-between align-items-center">
            <div class="pagination-info">
              <span class="f12-medium">
                Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to
                {{ Math.min(currentPage * itemsPerPage, sortedData.length) }} of
                {{ sortedData.length }} entries
              </span>
            </div>
            <nav aria-label="Table pagination">
              <ul class="pagination">
                <li class="page-item" :class="{ disabled: currentPage === 1 }">
                  <button
                    class="page-link"
                    @click="currentPage = Math.max(1, currentPage - 1)"
                    :disabled="currentPage === 1"
                  >
                    Previous
                  </button>
                </li>

                <li
                  v-for="page in visiblePages"
                  :key="page"
                  class="page-item"
                  :class="{ active: page === currentPage }"
                >
                  <button
                    class="page-link"
                    @click="currentPage = page"
                  >
                    {{ page }}
                  </button>
                </li>

                <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                  <button
                    class="page-link"
                    @click="currentPage = Math.min(totalPages, currentPage + 1)"
                    :disabled="currentPage === totalPages"
                  >
                    Next
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sortable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.sortable:hover {
  opacity: 0.8;
}

.sorted {
  background-color: rgba(255, 255, 255, 0.1);
}

.sort-active {
  opacity: 1;
}

.sort-desc {
  transform: rotate(180deg);
}

.pagination-wrapper {
  padding: 20px 0;
}

.pagination {
  margin: 0;
}

.pagination .page-link {
  color: var(--Primary);
  border: 1px solid #dee2e6;
  padding: 0.5rem 0.75rem;
  margin: 0 2px;
  border-radius: 4px;
  background-color: white;
  transition: all 0.2s ease;
}

.pagination .page-link:hover {
  background-color: var(--Primary);
  color: white;
  border-color: var(--Primary);
}

.pagination .page-item.active .page-link {
  background-color: var(--Primary);
  border-color: var(--Primary);
  color: white;
}

.pagination .page-item.disabled .page-link {
  color: #6c757d;
  background-color: #fff;
  border-color: #dee2e6;
  cursor: not-allowed;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.875rem;
}

.text-muted {
  color: #6c757d !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pagination-wrapper {
    flex-direction: column;
    gap: 1rem;
  }

  .pagination-info {
    text-align: center;
  }

  .pagination {
    justify-content: center;
  }
}
</style>
