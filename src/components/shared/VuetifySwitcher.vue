<template>
  <v-navigation-drawer
    v-model="drawer"
    location="right"
    temporary
    width="400"
    class="theme-switcher-drawer"
  >
    <!-- Header -->
    <v-toolbar
      color="surface"
      density="compact"
      class="px-4"
    >
      <v-toolbar-title class="text-h6 font-weight-medium">
        Settings
      </v-toolbar-title>
      <v-spacer />
      <v-btn
        icon="mdi-close"
        variant="text"
        size="small"
        @click="drawer = false"
      />
    </v-toolbar>

    <v-divider />

    <!-- Tab Navigation -->
    <v-tabs
      v-model="activeTab"
      color="primary"
      align-tabs="center"
      class="mb-4"
    >
      <v-tab value="style">
        <v-icon start>mdi-palette</v-icon>
        Theme Style
      </v-tab>
      <v-tab value="colors">
        <v-icon start>mdi-format-color-fill</v-icon>
        Theme Colors
      </v-tab>
    </v-tabs>

    <!-- Tab Content -->
    <v-card-text class="px-6">
      <v-tabs-window v-model="activeTab">
        <!-- Theme Style Tab -->
        <v-tabs-window-item value="style">
          <v-form class="theme-style-form">

            <!-- Layout Width -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Layout Width Style</v-card-title>
              <v-card-text>
                <v-radio-group
                  :model-value="themeStore.activeSelections.layoutWidth"
                  @update:model-value="handleLayoutWidthChange"
                  inline
                >
                  <v-radio
                    label="Boxed"
                    value="boxed"
                    color="primary"
                  />
                  <v-radio
                    label="Full Width"
                    value="full"
                    color="primary"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- Menu Style -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Vertical & Horizontal Menu Style</v-card-title>
              <v-card-text>
                <v-radio-group
                  :model-value="themeStore.activeSelections.menuStyle"
                  @update:model-value="handleMenuStyleChange"
                >
                  <v-radio
                    label="Menu Click"
                    value="default"
                    color="primary"
                  />
                  <v-radio
                    label="Icon Hover"
                    value="icon-hover"
                    color="primary"
                  />
                  <v-radio
                    label="Icon Default"
                    value="icon-default"
                    color="primary"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- Menu Position -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Menu Position</v-card-title>
              <v-card-text>
                <v-radio-group
                  :model-value="themeStore.activeSelections.menuPosition"
                  @update:model-value="handleMenuPositionChange"
                  inline
                >
                  <v-radio
                    label="Fixed"
                    value="fixed"
                    color="primary"
                  />
                  <v-radio
                    label="Scrollable"
                    value="scrollable"
                    color="primary"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- Header Position -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Header Position</v-card-title>
              <v-card-text>
                <v-radio-group
                  :model-value="themeStore.activeSelections.headerPosition"
                  @update:model-value="handleHeaderPositionChange"
                  inline
                >
                  <v-radio
                    label="Fixed"
                    value="fixed"
                    color="primary"
                  />
                  <v-radio
                    label="Scrollable"
                    value="scrollable"
                    color="primary"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- Loader -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Loader</v-card-title>
              <v-card-text>
                <v-radio-group
                  :model-value="themeStore.activeSelections.loader"
                  @update:model-value="handleLoaderChange"
                  inline
                >
                  <v-radio
                    label="Enable"
                    value="on"
                    color="primary"
                  />
                  <v-radio
                    label="Disable"
                    value="off"
                    color="primary"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- Theme Mode -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Theme Mode</v-card-title>
              <v-card-text>
                <v-radio-group
                  :model-value="themeStore.activeSelections.themeMode"
                  @update:model-value="handleThemeModeChange"
                  inline
                >
                  <v-radio
                    label="Light"
                    value="light"
                    color="primary"
                  />
                  <v-radio
                    label="Dark"
                    value="dark"
                    color="primary"
                  />
                </v-radio-group>
              </v-card-text>
            </v-card>

            <!-- Clear All Button -->
            <v-btn
              color="primary"
              variant="flat"
              block
              size="large"
              @click="themeStore.clearAllStyles"
              class="mb-4"
            >
              <v-icon start>mdi-refresh</v-icon>
              Clear All
            </v-btn>

            <!-- Debug Button -->
            <v-btn
              color="info"
              variant="outlined"
              block
              size="large"
              @click="debugCurrentState"
            >
              <v-icon start>mdi-bug</v-icon>
              Debug State
            </v-btn>

          </v-form>
        </v-tabs-window-item>

        <!-- Theme Colors Tab -->
        <v-tabs-window-item value="colors">
          <v-form class="theme-color-form">

            <!-- Menu Background Color -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Menu Background Color</v-card-title>
              <v-card-text>
                <div class="color-grid mb-3">
                  <v-btn
                    v-for="(color, key) in menuColors"
                    :key="key"
                    :color="color"
                    :variant="themeStore.activeSelections.menuColor === key ? 'flat' : 'outlined'"
                    size="large"
                    icon
                    class="color-btn"
                    @click="themeStore.applyMenuColor(key)"
                  >
                    <v-icon v-if="themeStore.activeSelections.menuColor === key">
                      mdi-check
                    </v-icon>
                  </v-btn>
                </div>
                <v-alert
                  type="info"
                  variant="tonal"
                  density="compact"
                  class="text-caption"
                >
                  Note: If you want to change color Menu dynamically change from below Theme Primary color picker
                </v-alert>
              </v-card-text>
            </v-card>

            <!-- Header Background Color -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Header Background Color</v-card-title>
              <v-card-text>
                <div class="color-grid mb-3">
                  <v-btn
                    v-for="(color, key) in headerColors"
                    :key="key"
                    :color="color"
                    :variant="themeStore.activeSelections.headerColor === key ? 'flat' : 'outlined'"
                    size="large"
                    icon
                    class="color-btn"
                    @click="themeStore.applyHeaderColor(key)"
                  >
                    <v-icon v-if="themeStore.activeSelections.headerColor === key">
                      mdi-check
                    </v-icon>
                  </v-btn>
                </div>
                <v-alert
                  type="info"
                  variant="tonal"
                  density="compact"
                  class="text-caption"
                >
                  Note: If you want to change color Header dynamically change from below Theme Primary color picker
                </v-alert>
              </v-card-text>
            </v-card>

            <!-- Theme Primary Color -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Theme Primary Color</v-card-title>
              <v-card-text>
                <div class="color-grid">
                  <v-btn
                    v-for="(color, key) in primaryColors"
                    :key="key"
                    :color="color"
                    :variant="themeStore.activeSelections.primaryColor === key ? 'flat' : 'outlined'"
                    size="large"
                    icon
                    class="color-btn"
                    @click="themeStore.applyPrimaryColor(key)"
                  >
                    <v-icon v-if="themeStore.activeSelections.primaryColor === key">
                      mdi-check
                    </v-icon>
                  </v-btn>
                </div>
              </v-card-text>
            </v-card>

            <!-- Theme Background Color -->
            <v-card class="mb-6" elevation="1">
              <v-card-title class="text-subtitle-1 pb-2">Theme Background Color</v-card-title>
              <v-card-text>
                <div class="color-grid">
                  <v-btn
                    v-for="(color, key) in backgroundColors"
                    :key="key"
                    :color="color"
                    :variant="themeStore.activeSelections.backgroundColor === key ? 'flat' : 'outlined'"
                    size="large"
                    icon
                    class="color-btn"
                    @click="themeStore.applyBackgroundColor(key)"
                  >
                    <v-icon v-if="themeStore.activeSelections.backgroundColor === key">
                      mdi-check
                    </v-icon>
                  </v-btn>
                </div>
              </v-card-text>
            </v-card>

            <!-- Clear Colors Button -->
            <v-btn
              color="primary"
              variant="flat"
              block
              size="large"
              @click="themeStore.clearAllColors"
            >
              <v-icon start>mdi-palette-outline</v-icon>
              Clear All Colors
            </v-btn>

          </v-form>
        </v-tabs-window-item>
      </v-tabs-window>
    </v-card-text>
  </v-navigation-drawer>

  <!-- Floating Action Button to open drawer -->
  <v-fab
    icon="mdi-cog"
    location="bottom end"
    size="large"
    color="primary"
    @click="drawer = true"
    class="theme-switcher-fab"
  />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTheme } from 'vuetify'
import { useThemeStore } from '@/stores/theme'

// Component state
const drawer = ref(false)
const activeTab = ref('style')

// Vuetify theme
const vuetifyTheme = useTheme()

// Theme store
const themeStore = useThemeStore()

// Color definitions matching the original implementation
const menuColors = computed(() => themeStore.colorDefinitions.menu)
const headerColors = computed(() => themeStore.colorDefinitions.header)
const primaryColors = computed(() => themeStore.colorDefinitions.primary)
const backgroundColors = computed(() => themeStore.colorDefinitions.background)

// Event handlers
const handleLayoutWidthChange = (value) => {
  themeStore.applyLayoutWidth(value === 'boxed')
}

const handleMenuStyleChange = (value) => {
  themeStore.applyMenuStyle(value)
}

const handleMenuPositionChange = (value) => {
  themeStore.applyMenuPosition(value === 'scrollable')
}

const handleHeaderPositionChange = (value) => {
  themeStore.applyHeaderPosition(value === 'scrollable')
}

const handleLoaderChange = (value) => {
  themeStore.applyLoader(value === 'on')
}

const handleThemeModeChange = (value) => {
  if (value === 'dark') {
    themeStore.applyDarkTheme()
  } else {
    themeStore.applyLightTheme()
  }
}

// Debug function
const debugCurrentState = () => {
  console.log('=== VUETIFY SWITCHER DEBUG ===')
  console.log('Active selections:', themeStore.activeSelections)
  console.log('Is dark mode:', themeStore.isDarkMode)
  console.log('Is boxed layout:', themeStore.isBoxedLayout)
  console.log('Menu style:', themeStore.activeSelections.menuStyle)
  console.log('===============================')
}

// Initialize on mount
onMounted(() => {
  console.log('Vuetify Switcher component mounted')

  // Initialize Vuetify theme in the store
  themeStore.initializeVuetifyTheme(vuetifyTheme)

  // Initialize theme from localStorage
  themeStore.initializeTheme()

  console.log('Vuetify Switcher initialized with theme:', themeStore.activeSelections.themeMode)
})
</script>

<style scoped>
.theme-switcher-drawer {
  z-index: 9999;
}

.theme-switcher-fab {
  z-index: 1000;
}

.color-grid {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.color-btn {
  min-width: 48px !important;
  min-height: 48px !important;
}

.theme-style-form,
.theme-color-form {
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

/* Custom scrollbar for better UX */
.theme-style-form::-webkit-scrollbar,
.theme-color-form::-webkit-scrollbar {
  width: 6px;
}

.theme-style-form::-webkit-scrollbar-track,
.theme-color-form::-webkit-scrollbar-track {
  background: transparent;
}

.theme-style-form::-webkit-scrollbar-thumb,
.theme-color-form::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.theme-style-form::-webkit-scrollbar-thumb:hover,
.theme-color-form::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
