<template>
  <div class="chart-showcase">
    <div class="row mb-4">
      <div class="col-12">
        <div class="wg-box style-1 bg-Gainsboro shadow-none">
          <div class="title mb-16">
            <div class="label-01">Dynamic Chart Showcase</div>
            <p class="text-muted">Select a chart type to see it rendered dynamically</p>
          </div>
          
          <div class="mb-3">
            <label for="chartTypeSelect" class="form-label">Choose Chart Type:</label>
            <select 
              id="chartTypeSelect" 
              v-model="selectedChartType" 
              class="form-select"
            >
              <option value="">Select a chart type...</option>
              <optgroup label="Candlestick Charts">
                <option 
                  v-for="chart in chartsByCategory.candlestick" 
                  :key="chart" 
                  :value="chart"
                >
                  {{ formatChartName(chart) }}
                </option>
              </optgroup>
              <optgroup label="Column Charts">
                <option 
                  v-for="chart in chartsByCategory.column" 
                  :key="chart" 
                  :value="chart"
                >
                  {{ formatChartName(chart) }}
                </option>
              </optgroup>
              <optgroup label="Line Charts">
                <option 
                  v-for="chart in chartsByCategory.line" 
                  :key="chart" 
                  :value="chart"
                >
                  {{ formatChartName(chart) }}
                </option>
              </optgroup>
              <optgroup label="Donut Charts">
                <option 
                  v-for="chart in chartsByCategory.donut" 
                  :key="chart" 
                  :value="chart"
                >
                  {{ formatChartName(chart) }}
                </option>
              </optgroup>
              <optgroup label="Small Charts">
                <option 
                  v-for="chart in chartsByCategory.small" 
                  :key="chart" 
                  :value="chart"
                >
                  {{ formatChartName(chart) }}
                </option>
              </optgroup>
            </select>
          </div>
          
          <div v-if="selectedChartType" class="chart-container">
            <h5>{{ formatChartName(selectedChartType) }}</h5>
            <ApexChart
              :chart-id="`showcase-${selectedChartType}`"
              :chart-type="selectedChartType"
              :options="chartOptions"
            />
          </div>
          
          <div v-else class="text-center py-5">
            <p class="text-muted">Select a chart type above to see it rendered</p>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Chart Grid Display -->
    <div class="row">
      <div class="col-12">
        <div class="wg-box style-1 bg-Gainsboro shadow-none">
          <div class="title mb-16">
            <div class="label-01">All Available Charts</div>
            <p class="text-muted">{{ availableChartTypes.length }} chart types available</p>
          </div>
          
          <div class="row">
            <div 
              v-for="chartType in availableChartTypes.slice(0, 6)" 
              :key="chartType"
              class="col-lg-4 col-md-6 mb-4"
            >
              <div class="card">
                <div class="card-header">
                  <h6 class="mb-0">{{ formatChartName(chartType) }}</h6>
                </div>
                <div class="card-body">
                  <ApexChart
                    :chart-id="`grid-${chartType}`"
                    :chart-type="chartType"
                    :options="{ chart: { height: 200 } }"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'
import { useCharts } from '@/composables/useCharts.js'

const { availableChartTypes, getChartsByCategory } = useCharts()

const selectedChartType = ref('')
const chartsByCategory = computed(() => getChartsByCategory())

const chartOptions = computed(() => ({
  chart: { 
    height: 400,
    toolbar: { show: true }
  }
}))

// Helper function to format chart names for display
const formatChartName = (chartType) => {
  return chartType
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
</script>

<style scoped>
.chart-showcase {
  padding: 20px 0;
}

.chart-container {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.card {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
}

.card-header {
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 16px;
}

.card-body {
  padding: 16px;
}
</style>
