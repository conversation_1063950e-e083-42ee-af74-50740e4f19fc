<template>
  <div class="small-chart-demo">
    <h3>📊 Small Chart Background Color Examples</h3>
    
    <div class="row">
      <div class="col-md-3">
        <div class="demo-card bg-primary">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-white">$2,847</h6>
              <div class="f12-medium text-white">+4% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-blue"
                chart-type="small-chart-1"
                :options="blueBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Blue Background</strong></p>
      </div>
      
      <div class="col-md-3">
        <div class="demo-card bg-success">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-white">$1,957</h6>
              <div class="f12-medium text-white">+2% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-green"
                chart-type="small-chart-2"
                :options="greenBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Green Background</strong></p>
      </div>
      
      <div class="col-md-3">
        <div class="demo-card bg-warning">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-dark">$3,247</h6>
              <div class="f12-medium text-dark">+6% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-yellow"
                chart-type="small-chart-3"
                :options="yellowBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Yellow Background</strong></p>
      </div>
      
      <div class="col-md-3">
        <div class="demo-card bg-danger">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-white">$4,157</h6>
              <div class="f12-medium text-white">+8% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-red"
                chart-type="small-chart-4"
                :options="redBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Red Background</strong></p>
      </div>
    </div>
    
    <div class="row mt-4">
      <div class="col-md-4">
        <div class="demo-card" style="background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-white">$5,847</h6>
              <div class="f12-medium text-white">+12% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-gradient"
                chart-type="small-chart-5"
                :options="gradientBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Gradient Background</strong></p>
      </div>
      
      <div class="col-md-4">
        <div class="demo-card bg-dark">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-white">$2,347</h6>
              <div class="f12-medium text-white">+3% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-dark"
                chart-type="small-chart-6"
                :options="darkBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Dark Background</strong></p>
      </div>
      
      <div class="col-md-4">
        <div class="demo-card bg-light">
          <div class="card-content">
            <div class="stats">
              <h6 class="text-dark">$3,647</h6>
              <div class="f12-medium text-dark">+5% (30 days)</div>
            </div>
            <div class="chart-small">
              <ApexChart
                chart-id="small-bg-light"
                chart-type="small-chart-1"
                :options="lightBackgroundOptions"
              />
            </div>
          </div>
        </div>
        <p class="text-center mt-2"><strong>Light Background</strong></p>
      </div>
    </div>
    
    <div class="interactive-section mt-4">
      <h4>🎨 Interactive Small Chart Background</h4>
      <div class="row">
        <div class="col-md-6">
          <div class="color-controls">
            <div class="mb-3">
              <label for="bgColor">Background Color:</label>
              <input type="color" id="bgColor" v-model="customBgColor" class="form-control color-input" />
            </div>
            <div class="mb-3">
              <label for="chartColor">Chart Line Color:</label>
              <input type="color" id="chartColor" v-model="customChartColor" class="form-control color-input" />
            </div>
            <div class="mb-3">
              <label for="fillColor">Fill Color:</label>
              <input type="color" id="fillColor" v-model="customFillColor" class="form-control color-input" />
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="demo-card" :style="{ background: customBgColor }">
            <div class="card-content">
              <div class="stats">
                <h6 :class="isLightColor(customBgColor) ? 'text-dark' : 'text-white'">$4,247</h6>
                <div class="f12-medium" :class="isLightColor(customBgColor) ? 'text-dark' : 'text-white'">
                  +7% (30 days)
                </div>
              </div>
              <div class="chart-small">
                <ApexChart
                  chart-id="small-bg-interactive"
                  chart-type="small-chart-1"
                  :options="interactiveOptions"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="code-examples mt-4">
      <h4>📝 Small Chart Background Code Examples</h4>
      <div class="code-block">
        <h5>Basic Small Chart with Background:</h5>
        <pre><code>&lt;ApexChart
  chart-type="small-chart-1"
  :options="{
    chart: { 
      background: '#e3f2fd' // Light blue
    },
    colors: ['#1976d2'] // Chart line color
  }"
/&gt;</code></pre>
      </div>
      
      <div class="code-block">
        <h5>Small Chart with Transparent Background:</h5>
        <pre><code>&lt;ApexChart
  chart-type="small-chart-1"
  :options="{
    chart: { 
      background: 'transparent'
    },
    colors: ['#ffffff'], // White line for dark backgrounds
    fill: {
      colors: ['rgba(255,255,255,0.3)'] // Semi-transparent fill
    }
  }"
/&gt;</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'

const customBgColor = ref('#e3f2fd')
const customChartColor = ref('#1976d2')
const customFillColor = ref('#bbdefb')

const blueBackgroundOptions = ref({
  chart: { background: '#e3f2fd' },
  colors: ['#1976d2'],
  fill: { colors: ['#bbdefb'] }
})

const greenBackgroundOptions = ref({
  chart: { background: '#e8f5e8' },
  colors: ['#2e7d32'],
  fill: { colors: ['#a5d6a7'] }
})

const yellowBackgroundOptions = ref({
  chart: { background: '#fff8e1' },
  colors: ['#f57f17'],
  fill: { colors: ['#fff176'] }
})

const redBackgroundOptions = ref({
  chart: { background: '#ffebee' },
  colors: ['#c62828'],
  fill: { colors: ['#ef9a9a'] }
})

const gradientBackgroundOptions = ref({
  chart: { background: 'transparent' },
  colors: ['#ffffff'],
  fill: { colors: ['rgba(255,255,255,0.3)'] }
})

const darkBackgroundOptions = ref({
  chart: { background: 'transparent' },
  colors: ['#ffffff'],
  fill: { colors: ['rgba(255,255,255,0.2)'] }
})

const lightBackgroundOptions = ref({
  chart: { background: '#f8f9fa' },
  colors: ['#495057'],
  fill: { colors: ['#dee2e6'] }
})

const interactiveOptions = computed(() => ({
  chart: { background: 'transparent' },
  colors: [customChartColor.value],
  fill: { colors: [customFillColor.value] }
}))

// Helper function to determine if a color is light or dark
const isLightColor = (color) => {
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000
  return brightness > 155
}
</script>

<style scoped>
.small-chart-demo {
  padding: 20px;
}

.demo-card {
  border-radius: 12px;
  padding: 20px;
  min-height: 120px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.stats h6 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.chart-small {
  width: 96px;
  height: 28px;
}

.interactive-section {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.color-controls {
  padding: 20px;
}

.color-input {
  width: 100px;
  height: 40px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.code-examples {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.code-block {
  margin-bottom: 20px;
}

.code-block h5 {
  color: #495057;
  margin-bottom: 10px;
}

.code-block pre {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
}

.code-block code {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}
</style>
