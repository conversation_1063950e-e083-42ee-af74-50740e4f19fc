<template>
  <div class="line-solution">
    <h3>🔧 Line Chart Two-line Solution</h3>

    <div class="alert alert-info">
      <h4>Problem Identified:</h4>
      <p>The second line in the two-line chart is nearly invisible because it uses a very transparent color: <code>#ffffff4d</code></p>
      <p>This color is white with only 30% opacity, making it barely visible on light backgrounds.</p>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>❌ Original (Problematic)</h4>
          <p>Second line barely visible due to transparent color</p>
          <ApexChart
            chart-id="original-problem"
            chart-type="line-chart-twoline"
            :options="{
              chart: { height: 300 },
              legend: { show: true },
              tooltip: { enabled: true }
            }"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>✅ Fixed (Better Colors)</h4>
          <p>Both lines clearly visible with proper colors</p>
          <ApexChart
            chart-id="fixed-solution"
            chart-type="line-chart-twoline"
            :options="fixedOptions"
          />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>✅ Non-stacked Version</h4>
          <p>Separate lines for better visibility</p>
          <ApexChart
            chart-id="unstacked-solution"
            chart-type="line-chart-twoline"
            :options="unStackedOptions"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>✅ Enhanced Version</h4>
          <p>Thicker lines with better styling</p>
          <ApexChart
            chart-id="enhanced-solution"
            chart-type="line-chart-twoline"
            :options="enhancedOptions"
          />
        </div>
      </div>
    </div>

    <div class="row">
      <div class="col-12">
        <div class="test-item">
          <h4>🎉 New Improved Version</h4>
          <p>Using the new <code>line-chart-twoline-improved</code> configuration</p>
          <ApexChart
            chart-id="improved-version"
            chart-type="line-chart-twoline-improved"
            :options="{
              chart: { height: 300 },
              title: { text: 'Improved Two-line Chart' }
            }"
          />
        </div>
      </div>
    </div>

    <div class="solution-box">
      <h4>💡 Solution Implemented:</h4>
      <p>Created <code>line-chart-twoline-improved.js</code> with the following fixes:</p>
      <ol>
        <li><strong>✅ Replaced transparent color:</strong> Changed <code>#ffffff4d</code> to visible <code>#FF6B6B</code></li>
        <li><strong>✅ Disabled stacking:</strong> Set <code>stacked: false</code> to show lines separately</li>
        <li><strong>✅ Enabled legend:</strong> Show legend to identify different series</li>
        <li><strong>✅ Increased stroke width:</strong> Made lines thicker for better visibility</li>
        <li><strong>✅ Fixed negative values:</strong> Removed negative value that caused stacking issues</li>
        <li><strong>✅ Enabled tooltips:</strong> Better user experience</li>
        <li><strong>✅ Added markers:</strong> Points are now visible on hover</li>
      </ol>
      <p><strong>Usage:</strong> <code>chart-type="line-chart-twoline-improved"</code></p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'

const fixedOptions = ref({
  chart: { height: 300 },
  colors: ['#D4FE75', '#FF6B6B'], // Replace transparent color
  legend: { show: true },
  tooltip: { enabled: true },
  stroke: { width: 2 }
})

const unStackedOptions = ref({
  chart: { height: 300, stacked: false }, // Disable stacking
  colors: ['#D4FE75', '#4ECDC4'],
  legend: { show: true },
  tooltip: { enabled: true },
  stroke: { width: 2 }
})

const enhancedOptions = ref({
  chart: { height: 300, stacked: false },
  colors: ['#D4FE75', '#FF6B6B'],
  legend: { show: true },
  tooltip: { enabled: true },
  stroke: {
    width: 3,
    curve: 'smooth'
  },
  markers: {
    size: 4,
    hover: { size: 6 }
  }
})
</script>

<style scoped>
.line-solution {
  padding: 20px;
}

.test-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.test-item h4 {
  margin-top: 0;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border: 1px solid transparent;
  border-radius: 4px;
}

.alert-info {
  color: #31708f;
  background-color: #d9edf7;
  border-color: #bce8f1;
}

.solution-box {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
}

.solution-box h4 {
  color: #28a745;
  margin-top: 0;
}

code {
  background: #f1f1f1;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: monospace;
}
</style>
