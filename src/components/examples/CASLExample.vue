<template>
  <div class="casl-example">
    <div class="container-fluid">
      <!-- Header -->
      <div class="row mb-4">
        <div class="col-12">
          <h2>CASL Permission Examples</h2>
          <p class="text-muted">
            This component demonstrates various CASL permission checks and conditional rendering.
          </p>
        </div>
      </div>

      <!-- Current User Info -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Current User Information</h5>
            </div>
            <div class="card-body">
              <div v-if="isAuthenticated" class="row">
                <div class="col-md-6">
                  <p><strong>Name:</strong> {{ userName }}</p>
                  <p><strong>Email:</strong> {{ user?.email }}</p>
                  <p><strong>Role:</strong> 
                    <span class="badge" :class="getRoleBadgeClass(userRole)">
                      {{ userRole }}
                    </span>
                  </p>
                </div>
                <div class="col-md-6">
                  <p><strong>Admin:</strong> {{ isAdmin ? 'Yes' : 'No' }}</p>
                  <p><strong>Moderator:</strong> {{ isModerator ? 'Yes' : 'No' }}</p>
                  <p><strong>Regular User:</strong> {{ isUser ? 'Yes' : 'No' }}</p>
                </div>
              </div>
              <div v-else class="text-center">
                <p class="text-muted">Not authenticated</p>
                <router-link to="/login" class="btn btn-primary">Login</router-link>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Permission Examples -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Permission-Based Rendering Examples</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <!-- Admin Only Content -->
                <div class="col-md-4 mb-3">
                  <h6>Admin Only</h6>
                  <CanAccess action="read" subject="AdminPanel">
                    <div class="alert alert-danger">
                      <i class="icon-shield me-2"></i>
                      This content is only visible to admins!
                    </div>
                    <button class="btn btn-danger btn-sm">Admin Action</button>
                  </CanAccess>
                  <CanAccess action="read" subject="AdminPanel" not>
                    <div class="alert alert-secondary">
                      <i class="icon-lock me-2"></i>
                      Admin content hidden
                    </div>
                  </CanAccess>
                </div>

                <!-- Moderator+ Content -->
                <div class="col-md-4 mb-3">
                  <h6>Moderator+ Access</h6>
                  <CanAccess role="moderator">
                    <div class="alert alert-warning">
                      <i class="icon-users me-2"></i>
                      Moderator content visible!
                    </div>
                    <button class="btn btn-warning btn-sm">Moderate Content</button>
                  </CanAccess>
                  <CanAccess role="moderator" not>
                    <div class="alert alert-secondary">
                      <i class="icon-lock me-2"></i>
                      Moderator content hidden
                    </div>
                  </CanAccess>
                </div>

                <!-- Authenticated Users -->
                <div class="col-md-4 mb-3">
                  <h6>Authenticated Users</h6>
                  <CanAccess require-auth>
                    <div class="alert alert-success">
                      <i class="icon-user me-2"></i>
                      Welcome, authenticated user!
                    </div>
                    <button class="btn btn-success btn-sm">User Action</button>
                  </CanAccess>
                  <CanAccess require-auth not>
                    <div class="alert alert-secondary">
                      <i class="icon-lock me-2"></i>
                      Please login to see content
                    </div>
                  </CanAccess>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action-Based Permissions -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Action-Based Permissions</h5>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-3 mb-3">
                  <h6>Create Posts</h6>
                  <CanAccess action="create" subject="Post">
                    <button class="btn btn-primary w-100">
                      <i class="icon-plus me-2"></i>
                      Create Post
                    </button>
                  </CanAccess>
                  <CanAccess action="create" subject="Post" not>
                    <button class="btn btn-secondary w-100" disabled>
                      <i class="icon-lock me-2"></i>
                      Cannot Create
                    </button>
                  </CanAccess>
                </div>

                <div class="col-md-3 mb-3">
                  <h6>Manage Posts</h6>
                  <CanAccess action="manage" subject="Post">
                    <button class="btn btn-warning w-100">
                      <i class="icon-settings me-2"></i>
                      Manage Posts
                    </button>
                  </CanAccess>
                  <CanAccess action="manage" subject="Post" not>
                    <button class="btn btn-secondary w-100" disabled>
                      <i class="icon-lock me-2"></i>
                      Cannot Manage
                    </button>
                  </CanAccess>
                </div>

                <div class="col-md-3 mb-3">
                  <h6>Manage Users</h6>
                  <CanAccess action="manage" subject="User">
                    <button class="btn btn-danger w-100">
                      <i class="icon-users me-2"></i>
                      Manage Users
                    </button>
                  </CanAccess>
                  <CanAccess action="manage" subject="User" not>
                    <button class="btn btn-secondary w-100" disabled>
                      <i class="icon-lock me-2"></i>
                      Cannot Manage
                    </button>
                  </CanAccess>
                </div>

                <div class="col-md-3 mb-3">
                  <h6>Settings</h6>
                  <CanAccess action="read" subject="Settings">
                    <button class="btn btn-info w-100">
                      <i class="icon-settings me-2"></i>
                      View Settings
                    </button>
                  </CanAccess>
                  <CanAccess action="read" subject="Settings" not>
                    <button class="btn btn-secondary w-100" disabled>
                      <i class="icon-lock me-2"></i>
                      No Access
                    </button>
                  </CanAccess>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Permission Check Results -->
      <div class="row mb-4">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Permission Check Results</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Permission</th>
                      <th>Result</th>
                      <th>Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><code>can('read', 'Dashboard')</code></td>
                      <td>
                        <span :class="can('read', 'Dashboard') ? 'badge bg-success' : 'badge bg-danger'">
                          {{ can('read', 'Dashboard') ? 'Allowed' : 'Denied' }}
                        </span>
                      </td>
                      <td>Can access dashboard</td>
                    </tr>
                    <tr>
                      <td><code>can('read', 'AdminPanel')</code></td>
                      <td>
                        <span :class="canAccessAdminPanel ? 'badge bg-success' : 'badge bg-danger'">
                          {{ canAccessAdminPanel ? 'Allowed' : 'Denied' }}
                        </span>
                      </td>
                      <td>Can access admin panel</td>
                    </tr>
                    <tr>
                      <td><code>can('create', 'Post')</code></td>
                      <td>
                        <span :class="canCreatePosts ? 'badge bg-success' : 'badge bg-danger'">
                          {{ canCreatePosts ? 'Allowed' : 'Denied' }}
                        </span>
                      </td>
                      <td>Can create new posts</td>
                    </tr>
                    <tr>
                      <td><code>can('manage', 'User')</code></td>
                      <td>
                        <span :class="canManageUsers ? 'badge bg-success' : 'badge bg-danger'">
                          {{ canManageUsers ? 'Allowed' : 'Denied' }}
                        </span>
                      </td>
                      <td>Can manage other users</td>
                    </tr>
                    <tr>
                      <td><code>can('manage', 'Post')</code></td>
                      <td>
                        <span :class="canManagePosts ? 'badge bg-success' : 'badge bg-danger'">
                          {{ canManagePosts ? 'Allowed' : 'Denied' }}
                        </span>
                      </td>
                      <td>Can manage all posts</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Role Switching Demo -->
      <div class="row">
        <div class="col-12">
          <div class="card">
            <div class="card-header">
              <h5 class="card-title mb-0">Role Switching Demo</h5>
            </div>
            <div class="card-body">
              <p class="text-muted mb-3">
                Switch between different user roles to see how permissions change in real-time.
              </p>
              <div class="btn-group" role="group">
                <button 
                  class="btn btn-outline-danger"
                  @click="switchRole('admin')"
                  :class="{ active: userRole === 'admin' }"
                >
                  Admin
                </button>
                <button 
                  class="btn btn-outline-warning"
                  @click="switchRole('moderator')"
                  :class="{ active: userRole === 'moderator' }"
                >
                  Moderator
                </button>
                <button 
                  class="btn btn-outline-primary"
                  @click="switchRole('user')"
                  :class="{ active: userRole === 'user' }"
                >
                  User
                </button>
                <button 
                  class="btn btn-outline-secondary"
                  @click="logout"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuth } from '@/composables/useAuth'
import { useAuthStore } from '@/stores/auth'
import CanAccess from '@/components/auth/CanAccess.vue'

const {
  user,
  isAuthenticated,
  userName,
  userRole,
  isAdmin,
  isModerator,
  isUser,
  can,
  canAccessAdminPanel,
  canManageUsers,
  canCreatePosts,
  canManagePosts,
  logout
} = useAuth()

const authStore = useAuthStore()

const getRoleBadgeClass = (role) => {
  const classes = {
    admin: 'bg-danger',
    moderator: 'bg-warning',
    user: 'bg-primary'
  }
  return classes[role] || 'bg-secondary'
}

const switchRole = async (newRole) => {
  if (!isAuthenticated.value) {
    // Login with the specified role
    const credentials = {
      admin: { email: '<EMAIL>', password: 'admin123' },
      moderator: { email: '<EMAIL>', password: 'mod123' },
      user: { email: '<EMAIL>', password: 'user123' }
    }
    
    await authStore.login(credentials[newRole])
  } else {
    // Update current user's role (for demo purposes)
    authStore.updateUser({ role: newRole })
  }
}
</script>

<style scoped>
.casl-example {
  padding: 2rem 0;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.badge {
  font-size: 0.75rem;
}

.btn-group .btn.active {
  background-color: var(--bs-primary);
  border-color: var(--bs-primary);
  color: white;
}

code {
  background-color: #f8f9fa;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}
</style>
