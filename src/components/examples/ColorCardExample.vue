<template>
  <div class="color-card-example">
    <h2>Dynamic ColorCard Examples</h2>
    
    <!-- Basic Cards with Different Backgrounds -->
    <div class="example-section">
      <h3>Basic Cards with Different Backgrounds</h3>
      <div class="row">
        <div class="col-md-3" v-for="(card, index) in basicCards" :key="index">
          <ColorCard
            :wallet-name="card.walletName"
            :amount="card.amount"
            :card-number="card.cardNumber"
            :background="card.background"
            :clickable="true"
            @click="handleCardClick"
          />
        </div>
      </div>
    </div>
    
    <!-- Interactive Cards -->
    <div class="example-section mt-5">
      <h3>Interactive Cards with Custom Styling</h3>
      <div class="row">
        <div class="col-md-4">
          <ColorCard
            wallet-name="Premium Account"
            :amount="125000.50"
            card-number="4532 1234 5678 9012"
            background="bg-1"
            :clickable="true"
            card-class="premium-card"
            amount-class="label-01 text-primary"
            @click="handleCardClick"
            @hover="handleCardHover"
          >
            <!-- Custom icon slot -->
            <template #icon>
              <div class="custom-icon premium-icon">
                <i class="icon-star"></i>
              </div>
            </template>
          </ColorCard>
        </div>
        
        <div class="col-md-4">
          <ColorCard
            wallet-name="Business Wallet"
            :amount="75000"
            card-number="5555 4444 3333 2222"
            background="bg-2"
            :clickable="true"
            @click="handleCardClick"
          >
            <!-- Custom wallet name slot -->
            <template #walletName="{ walletName, background }">
              <div class="custom-wallet-name">
                <i class="icon-briefcase"></i>
                {{ walletName }}
              </div>
            </template>
            
            <!-- Custom amount slot -->
            <template #amount="{ formattedAmount }">
              <div class="business-amount">
                {{ formattedAmount }}
                <small class="growth-indicator">+12.5%</small>
              </div>
            </template>
          </ColorCard>
        </div>
        
        <div class="col-md-4">
          <ColorCard
            wallet-name="Savings Account"
            :amount="32500.75"
            card-number="6011 1111 2222 3333"
            background="bg-3"
            :clickable="true"
            @click="handleCardClick"
          >
            <!-- Custom additional content -->
            <template #additional>
              <div class="savings-info">
                <small class="interest-rate">APY: 2.5%</small>
              </div>
            </template>
          </ColorCard>
        </div>
      </div>
    </div>
    
    <!-- Cards with Custom Content -->
    <div class="example-section mt-5">
      <h3>Cards with Custom Content and Icons</h3>
      <div class="row">
        <div class="col-md-6">
          <ColorCard
            wallet-name="Crypto Wallet"
            amount="₿ 2.5847"
            card-number="******************************************"
            background="bg-4"
            :clickable="true"
            @click="handleCardClick"
          >
            <!-- Custom icon for crypto -->
            <template #icon>
              <div class="crypto-icon">
                <svg width="35" height="35" viewBox="0 0 35 35" fill="none">
                  <circle cx="17.5" cy="17.5" r="17.5" fill="#F7931A"/>
                  <path d="M25.5 14.5c.5-3.5-2-5.5-5.5-6.5l1-4.5-2.5-.5-1 4c-.5-.5-1.5-.5-2-.5l1-4-2.5-.5-1 4.5c-.5 0-1 0-1.5-.5v0l-3.5-.5-.5 2.5s2 .5 2 .5c1 0 1.5.5 1.5 1.5l-1.5 6.5c0 .5 0 1-.5 1.5 0 0-2-.5-2-.5l-1 3 3.5.5c.5 0 1.5.5 2 .5l-1 4.5 2.5.5 1-4.5c.5.5 1.5.5 2 .5l-1 4.5 2.5.5 1-4.5c4.5-.5 7.5-2.5 8-6.5.5-3-0.5-4.5-2.5-5.5 1.5-.5 2.5-2 2-4z" fill="white"/>
                </svg>
              </div>
            </template>
            
            <!-- Custom card number for crypto -->
            <template #cardNumber="{ cardNumber }">
              <div class="crypto-address">
                {{ cardNumber.substring(0, 8) }}...{{ cardNumber.substring(cardNumber.length - 8) }}
              </div>
            </template>
          </ColorCard>
        </div>
        
        <div class="col-md-6">
          <ColorCard
            wallet-name="Investment Portfolio"
            :amount="250000"
            card-number="INV-2024-001"
            background="bg-1"
            :clickable="true"
            @click="handleCardClick"
          >
            <!-- Custom wallet name with icon -->
            <template #walletName>
              <div class="investment-header">
                <i class="icon-trending-up"></i>
                Investment Portfolio
              </div>
            </template>
            
            <!-- Custom amount with performance -->
            <template #amount="{ formattedAmount }">
              <div class="investment-amount">
                {{ formattedAmount }}
                <div class="performance-indicator positive">
                  <i class="icon-arrow-up"></i>
                  +15.2%
                </div>
              </div>
            </template>
            
            <!-- Custom additional info -->
            <template #additional>
              <div class="portfolio-stats">
                <span class="stat">
                  <small>Stocks: 65%</small>
                </span>
                <span class="stat">
                  <small>Bonds: 35%</small>
                </span>
              </div>
            </template>
          </ColorCard>
        </div>
      </div>
    </div>
    
    <!-- Disabled and Non-clickable Cards -->
    <div class="example-section mt-5">
      <h3>Disabled and Non-clickable Cards</h3>
      <div class="row">
        <div class="col-md-4">
          <ColorCard
            wallet-name="Locked Account"
            amount="$0.00"
            card-number="**** **** **** ****"
            background="bg-2"
            :disabled="true"
            :clickable="true"
          />
        </div>
        
        <div class="col-md-4">
          <ColorCard
            wallet-name="Display Only"
            :amount="15000"
            card-number="1234 5678 9012 3456"
            background="bg-3"
            :clickable="false"
          />
        </div>
        
        <div class="col-md-4">
          <ColorCard
            wallet-name="Pending Verification"
            amount="Pending..."
            card-number="Verification Required"
            background="bg-4"
            :show-icon="false"
          />
        </div>
      </div>
    </div>
    
    <!-- Event Log -->
    <div class="event-log mt-5">
      <h3>Event Log</h3>
      <div class="log-container">
        <div v-for="(event, index) in eventLog" :key="index" class="log-entry">
          <span class="timestamp">{{ event.timestamp }}</span>
          <span class="event-type">{{ event.type }}</span>
          <span class="event-data">{{ event.data }}</span>
        </div>
      </div>
      <button @click="clearLog" class="btn btn-sm btn-outline-secondary mt-2">Clear Log</button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ColorCard from '@/components/shared/ColorCard.vue'

// Basic cards data
const basicCards = ref([
  {
    walletName: 'Main Wallet',
    amount: 48200.00,
    cardNumber: '6219 8610 2888 8075',
    background: 'bg-1'
  },
  {
    walletName: 'Secondary Wallet',
    amount: 25750.50,
    cardNumber: '4532 1234 5678 9012',
    background: 'bg-2'
  },
  {
    walletName: 'Emergency Fund',
    amount: 10000.00,
    cardNumber: '5555 4444 3333 2222',
    background: 'bg-3'
  },
  {
    walletName: 'Travel Fund',
    amount: 5500.25,
    cardNumber: '6011 1111 2222 3333',
    background: 'bg-4'
  }
])

// Event logging
const eventLog = ref([])

function logEvent(type, data) {
  eventLog.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    data: JSON.stringify(data)
  })
  
  // Keep only last 10 events
  if (eventLog.value.length > 10) {
    eventLog.value = eventLog.value.slice(0, 10)
  }
}

function clearLog() {
  eventLog.value = []
}

// Event handlers
function handleCardClick(cardData) {
  logEvent('card-click', {
    wallet: cardData.walletName,
    amount: cardData.amount,
    background: cardData.background
  })
  console.log('Card clicked:', cardData)
}

function handleCardHover(hoverData) {
  logEvent('card-hover', {
    type: hoverData.type,
    wallet: hoverData.walletName
  })
}
</script>

<style scoped>
.color-card-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
}

.example-section h3 {
  margin-bottom: 20px;
  color: #333;
}

/* Custom card styling */
.premium-card {
  border: 2px solid gold;
  box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);
}

.custom-icon.premium-icon {
  background: gold;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
}

.custom-wallet-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6D6D6D;
  font-size: 12px;
  font-weight: 500;
}

.business-amount {
  position: relative;
}

.growth-indicator {
  color: #2BC155;
  font-weight: bold;
  margin-left: 8px;
}

.savings-info {
  margin-top: 8px;
}

.interest-rate {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  color: #fff;
}

.crypto-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.crypto-address {
  font-family: monospace;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

.investment-header {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #6D6D6D;
  font-size: 12px;
  font-weight: 500;
}

.investment-amount {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.performance-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  font-weight: bold;
}

.performance-indicator.positive {
  color: #2BC155;
}

.portfolio-stats {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.stat small {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  color: #fff;
}

/* Event log styling */
.event-log {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.log-entry {
  display: block;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.timestamp {
  color: #666;
  margin-right: 10px;
}

.event-type {
  color: #007bff;
  font-weight: bold;
  margin-right: 10px;
}

.event-data {
  color: #333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .col-md-3,
  .col-md-4,
  .col-md-6 {
    margin-bottom: 20px;
  }
}
</style>
