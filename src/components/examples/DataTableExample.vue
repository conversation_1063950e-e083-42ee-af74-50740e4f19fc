<template>
  <div class="data-table-example">
    <h2>Dynamic DataTable Examples</h2>
    
    <!-- Basic Transaction Table -->
    <div class="example-section">
      <h3>Transaction Table</h3>
      <DataTable
        :data="transactionData"
        :columns="transactionColumns"
        title="Transaction History"
        :searchable="true"
        :sortable="true"
        :selectable="true"
        :pagination="true"
        :items-per-page="5"
        @row-select="handleRowSelect"
        @row-click="handleRowClick"
        @export-data="handleExport"
      >
        <!-- Custom status column -->
        <template #column-status="{ value, row }">
          <div class="box-status" :class="getCustomStatusClass(value)">
            <i v-if="value === 'COMPLETED'" class="icon icon-check"></i>
            <span class="font-poppins">{{ value }}</span>
          </div>
        </template>
        
        <!-- Custom amount column with formatting -->
        <template #column-amount="{ value }">
          <div class="f12-medium" style="font-weight: bold; color: #28a745;">
            {{ formatCurrency(value) }}
          </div>
        </template>
      </DataTable>
    </div>
    
    <!-- User Table -->
    <div class="example-section mt-5">
      <h3>User Management Table</h3>
      <DataTable
        :data="userData"
        :columns="userColumns"
        title="Users"
        :searchable="true"
        :sortable="true"
        :selectable="false"
        :pagination="true"
        :items-per-page="3"
        @row-click="handleUserClick"
      >
        <!-- Custom avatar column -->
        <template #column-avatar="{ value, row }">
          <div class="wrap-image">
            <div class="image">
              <img :src="value || '/images/avatar/default.png'" :alt="row.name" />
            </div>
            <div class="f12-medium">{{ row.name }}</div>
          </div>
        </template>
        
        <!-- Custom role column with badges -->
        <template #column-role="{ value }">
          <span class="badge" :class="getRoleBadgeClass(value)">
            {{ value }}
          </span>
        </template>
      </DataTable>
    </div>
    
    <!-- Simple Product Table -->
    <div class="example-section mt-5">
      <h3>Product Inventory</h3>
      <DataTable
        :data="productData"
        :columns="productColumns"
        title="Products"
        :searchable="true"
        :sortable="true"
        :selectable="true"
        :pagination="false"
        :show-export="false"
        @row-select="handleProductSelect"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import DataTable from '@/components/shared/DataTable.vue'

// Transaction data
const transactionData = ref([
  {
    id: 1,
    transactionId: '#124567899654',
    date: '2024-01-24T17:20:00',
    from: { name: 'Marquezz', avatar: '/images/avatar/user-3.png' },
    to: { name: 'Jarinas Tom', avatar: '/images/avatar/user-4.png' },
    coin: { name: 'Bitcoin', image: '/images/item/coin-1.png' },
    amount: 455.00,
    status: 'COMPLETED'
  },
  {
    id: 2,
    transactionId: '#124567899655',
    date: '2024-01-24T16:15:00',
    from: { name: 'Alice Johnson', avatar: '/images/avatar/user-1.png' },
    to: { name: 'Bob Smith', avatar: '/images/avatar/user-2.png' },
    coin: { name: 'Ethereum', image: '/images/item/coin-2.png' },
    amount: 1250.75,
    status: 'PENDING'
  },
  {
    id: 3,
    transactionId: '#124567899656',
    date: '2024-01-24T15:30:00',
    from: { name: 'Charlie Brown', avatar: '/images/avatar/user-5.png' },
    to: { name: 'Diana Prince', avatar: '/images/avatar/user-6.png' },
    coin: { name: 'Monero', image: '/images/item/coin-3.png' },
    amount: 89.50,
    status: 'CANCELED'
  }
])

// Transaction columns configuration
const transactionColumns = ref([
  {
    key: 'transactionId',
    label: 'Transaction ID',
    sortable: true
  },
  {
    key: 'date',
    label: 'Date',
    type: 'date',
    sortable: true
  },
  {
    key: 'from.name',
    label: 'From',
    type: 'image',
    imageKey: 'from.avatar',
    textKey: 'from.name',
    sortable: true
  },
  {
    key: 'to.name',
    label: 'To',
    type: 'image',
    imageKey: 'to.avatar',
    textKey: 'to.name',
    sortable: true
  },
  {
    key: 'coin.name',
    label: 'Coin',
    type: 'image',
    imageKey: 'coin.image',
    imageClass: 'style-1',
    sortable: true
  },
  {
    key: 'amount',
    label: 'Amount',
    type: 'currency',
    sortable: true
  },
  {
    key: 'status',
    label: 'Status',
    type: 'status',
    sortable: true
  }
])

// User data
const userData = ref([
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: '/images/avatar/user-1.png',
    role: 'Admin',
    lastLogin: '2024-01-24T10:30:00',
    status: 'Active'
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    avatar: '/images/avatar/user-2.png',
    role: 'User',
    lastLogin: '2024-01-23T14:20:00',
    status: 'Active'
  },
  {
    id: 3,
    name: 'Mike Johnson',
    email: '<EMAIL>',
    avatar: '/images/avatar/user-3.png',
    role: 'Moderator',
    lastLogin: '2024-01-22T09:15:00',
    status: 'Inactive'
  }
])

// User columns configuration
const userColumns = ref([
  {
    key: 'avatar',
    label: 'User',
    sortable: false
  },
  {
    key: 'email',
    label: 'Email',
    sortable: true
  },
  {
    key: 'role',
    label: 'Role',
    sortable: true
  },
  {
    key: 'lastLogin',
    label: 'Last Login',
    type: 'date',
    format: 'short',
    sortable: true
  },
  {
    key: 'status',
    label: 'Status',
    type: 'status',
    sortable: true
  }
])

// Product data
const productData = ref([
  {
    id: 1,
    name: 'Laptop Pro',
    category: 'Electronics',
    price: 1299.99,
    stock: 45,
    status: 'In Stock'
  },
  {
    id: 2,
    name: 'Wireless Headphones',
    category: 'Audio',
    price: 199.99,
    stock: 0,
    status: 'Out of Stock'
  },
  {
    id: 3,
    name: 'Smart Watch',
    category: 'Wearables',
    price: 299.99,
    stock: 12,
    status: 'Low Stock'
  }
])

// Product columns configuration
const productColumns = ref([
  {
    key: 'name',
    label: 'Product Name',
    sortable: true
  },
  {
    key: 'category',
    label: 'Category',
    sortable: true
  },
  {
    key: 'price',
    label: 'Price',
    type: 'currency',
    sortable: true
  },
  {
    key: 'stock',
    label: 'Stock',
    sortable: true
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true
  }
])

// Event handlers
function handleRowSelect(event) {
  console.log('Row selected:', event)
}

function handleRowClick(row) {
  console.log('Row clicked:', row)
}

function handleUserClick(user) {
  console.log('User clicked:', user)
}

function handleProductSelect(event) {
  console.log('Product selected:', event)
}

function handleExport(event) {
  console.log('Export data:', event)
  // Here you could implement actual export functionality
}

// Helper functions for custom styling
function getCustomStatusClass(status) {
  switch (status) {
    case 'COMPLETED':
      return 'bg-YellowGreen'
    case 'PENDING':
      return 'bg-LightGray'
    case 'CANCELED':
      return 'bg-LightGray type-red'
    default:
      return 'bg-LightGray'
  }
}

function getRoleBadgeClass(role) {
  switch (role) {
    case 'Admin':
      return 'badge-danger'
    case 'Moderator':
      return 'badge-warning'
    case 'User':
      return 'badge-primary'
    default:
      return 'badge-secondary'
  }
}

function formatCurrency(amount) {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}
</script>

<style scoped>
.data-table-example {
  padding: 20px;
}

.example-section {
  margin-bottom: 40px;
}

.example-section h3 {
  margin-bottom: 20px;
  color: #333;
}

.badge {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: 0.25rem;
  text-transform: uppercase;
}

.badge-primary {
  background-color: #007bff;
  color: white;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}
</style>
