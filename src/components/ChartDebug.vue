<template>
  <div class="chart-debug">
    <h3>Chart Configuration Debug</h3>
    
    <div class="debug-section">
      <h4>Chart Type: {{ selectedType }}</h4>
      <select v-model="selectedType" class="form-select mb-3">
        <option value="candlestick-1">Candlestick 1</option>
        <option value="column-chart-1">Column Chart 1</option>
        <option value="line-chart-twoline">Line Chart Two-line</option>
        <option value="small-chart-1">Small Chart 1</option>
      </select>
      
      <div class="row">
        <div class="col-md-6">
          <h5>Raw Configuration</h5>
          <pre class="config-display">{{ JSON.stringify(rawConfig, null, 2) }}</pre>
        </div>
        <div class="col-md-6">
          <h5>Final Configuration</h5>
          <pre class="config-display">{{ JSON.stringify(finalConfig, null, 2) }}</pre>
        </div>
      </div>
      
      <div class="chart-container mt-4">
        <h5>Rendered Chart</h5>
        <ApexChart
          :chart-id="`debug-${selectedType}`"
          :chart-type="selectedType"
          :options="chartOptions"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'
import { getChartConfig } from '@/plugins/charts.js'

const selectedType = ref('candlestick-1')

const rawConfig = computed(() => {
  return getChartConfig(selectedType.value)
})

const chartOptions = computed(() => {
  const baseOptions = {
    chart: { height: 300 }
  }
  
  // Add specific options based on chart type
  if (selectedType.value.startsWith('candlestick')) {
    return {
      ...baseOptions,
      chart: { height: 350 },
      xaxis: { 
        type: 'datetime',
        labels: { show: true }
      },
      yaxis: { show: true }
    }
  } else if (selectedType.value.startsWith('column-chart')) {
    return {
      ...baseOptions,
      xaxis: { labels: { show: true } },
      yaxis: { show: true },
      dataLabels: { enabled: true }
    }
  }
  
  return baseOptions
})

const finalConfig = computed(() => {
  const base = rawConfig.value || {}
  return {
    ...base,
    ...chartOptions.value,
    chart: {
      ...base.chart,
      ...chartOptions.value.chart
    }
  }
})
</script>

<style scoped>
.chart-debug {
  padding: 20px;
}

.debug-section {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.config-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-size: 12px;
  max-height: 400px;
  overflow-y: auto;
}

.chart-container {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 20px;
  background: white;
}

.form-select {
  max-width: 300px;
}
</style>
