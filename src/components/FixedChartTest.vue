<template>
  <div class="fixed-test">
    <h3>Fixed Chart Test</h3>
    
    <div class="test-item">
      <h4>Working Candlestick Chart</h4>
      <ApexChart
        chart-id="fixed-candlestick"
        :series="candlestickSeries"
        :options="candlestickOptions"
      />
    </div>
    
    <div class="test-item">
      <h4>Working Column Chart</h4>
      <ApexChart
        chart-id="fixed-column"
        :series="columnSeries"
        :options="columnOptions"
      />
    </div>
    
    <div class="test-item">
      <h4>Dynamic Candlestick (Fixed Data)</h4>
      <ApexChart
        chart-id="dynamic-candlestick-fixed"
        chart-type="candlestick-1"
        :series="simpleCandlestickSeries"
        :options="{ chart: { height: 350 } }"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'

// Simple working candlestick data
const candlestickSeries = ref([
  {
    data: [
      { x: '2023-01-01', y: [100, 120, 95, 110] },
      { x: '2023-01-02', y: [110, 125, 105, 115] },
      { x: '2023-01-03', y: [115, 130, 110, 125] },
      { x: '2023-01-04', y: [125, 135, 120, 130] },
      { x: '2023-01-05', y: [130, 140, 125, 135] },
    ]
  }
])

const candlestickOptions = ref({
  chart: {
    type: 'candlestick',
    height: 350,
    toolbar: { show: false }
  },
  plotOptions: {
    candlestick: {
      colors: {
        upward: '#00B746',
        downward: '#EF403C'
      }
    }
  },
  xaxis: {
    type: 'category'
  }
})

// Simple column chart data
const columnSeries = ref([
  {
    name: 'Sales',
    data: [44, 55, 41, 67, 22, 43, 21, 44, 55, 41, 67]
  },
  {
    name: 'Revenue',
    data: [13, 23, 20, 8, 13, 27, 33, 13, 23, 20, 8]
  }
])

const columnOptions = ref({
  chart: {
    type: 'bar',
    height: 350,
    stacked: true,
    toolbar: { show: false }
  },
  plotOptions: {
    bar: {
      columnWidth: '50%'
    }
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov']
  },
  colors: ['#008FFB', '#00E396']
})

// Simplified candlestick data for dynamic test
const simpleCandlestickSeries = ref([
  {
    data: [
      { x: 'Jan', y: [100, 120, 95, 110] },
      { x: 'Feb', y: [110, 125, 105, 115] },
      { x: 'Mar', y: [115, 130, 110, 125] },
      { x: 'Apr', y: [125, 135, 120, 130] },
    ]
  }
])
</script>

<style scoped>
.fixed-test {
  padding: 20px;
}

.test-item {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-item h4 {
  margin-top: 0;
  color: #333;
}
</style>
