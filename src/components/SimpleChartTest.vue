<template>
  <div class="simple-test">
    <h3>Simple Chart Test</h3>
    
    <!-- Test with minimal configuration -->
    <div class="test-item">
      <h4>Candlestick Chart (Raw)</h4>
      <div id="raw-candlestick"></div>
    </div>
    
    <div class="test-item">
      <h4>Candlestick Chart (Dynamic Component)</h4>
      <ApexChart
        chart-id="dynamic-candlestick-test"
        chart-type="candlestick-1"
      />
    </div>
    
    <div class="test-item">
      <h4>Column Chart (Dynamic Component)</h4>
      <ApexChart
        chart-id="dynamic-column-test"
        chart-type="column-chart-1"
      />
    </div>
    
    <div class="test-item">
      <h4>Registry Info</h4>
      <p>Available charts: {{ availableTypes.length }}</p>
      <p>Candlestick config exists: {{ !!candlestickConfig }}</p>
      <p>Column config exists: {{ !!columnConfig }}</p>
    </div>
  </div>
</template>

<script setup>
import { onMounted, computed } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'
import { getChartConfig, getAvailableChartTypes } from '@/plugins/charts.js'
import ApexCharts from 'apexcharts'

const availableTypes = computed(() => getAvailableChartTypes())
const candlestickConfig = computed(() => getChartConfig('candlestick-1'))
const columnConfig = computed(() => getChartConfig('column-chart-1'))

onMounted(() => {
  // Test raw ApexCharts rendering
  const config = getChartConfig('candlestick-1')
  if (config) {
    const chart = new ApexCharts(document.querySelector('#raw-candlestick'), {
      ...config,
      chart: { ...config.chart, height: 300 }
    })
    chart.render()
  }
})
</script>

<style scoped>
.simple-test {
  padding: 20px;
}

.test-item {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-item h4 {
  margin-top: 0;
}

#raw-candlestick {
  height: 300px;
}
</style>
