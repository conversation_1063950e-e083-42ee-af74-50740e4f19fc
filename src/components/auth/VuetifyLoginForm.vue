<template>
  <v-form
    ref="form"
    v-model="valid"
    @submit.prevent="handleSubmit"
    class="login-form"
  >
    <!-- Email Field -->
    <v-text-field
      v-model="email"
      :rules="emailRules"
      label="Email Address"
      type="email"
      variant="outlined"
      prepend-inner-icon="mdi-email"
      class="mb-4"
      :loading="loading"
      :disabled="loading"
      autocomplete="email"
      required
    />

    <!-- Password Field -->
    <v-text-field
      v-model="password"
      :rules="passwordRules"
      :type="showPassword ? 'text' : 'password'"
      label="Password"
      variant="outlined"
      prepend-inner-icon="mdi-lock"
      :append-inner-icon="showPassword ? 'mdi-eye' : 'mdi-eye-off'"
      @click:append-inner="showPassword = !showPassword"
      class="mb-4"
      :loading="loading"
      :disabled="loading"
      autocomplete="current-password"
      required
    />

    <!-- Remember Me & Forgot Password -->
    <div class="d-flex justify-space-between align-center mb-6">
      <v-checkbox
        v-model="rememberMe"
        label="Remember me"
        density="compact"
        hide-details
        :disabled="loading"
      />
      
      <v-btn
        variant="text"
        size="small"
        color="primary"
        :disabled="loading"
        @click="handleForgotPassword"
      >
        Forgot Password?
      </v-btn>
    </div>

    <!-- Submit Button -->
    <v-btn
      type="submit"
      color="primary"
      variant="flat"
      size="large"
      block
      :loading="loading"
      :disabled="!valid || loading"
      class="mb-4"
    >
      <v-icon start>mdi-login</v-icon>
      Sign In
    </v-btn>

    <!-- Quick Login Buttons -->
    <div class="quick-login-section">
      <v-divider class="mb-4">
        <span class="text-body-2 text-grey px-3">Quick Login</span>
      </v-divider>
      
      <v-row dense>
        <v-col cols="4">
          <v-btn
            variant="outlined"
            size="small"
            block
            :disabled="loading"
            @click="quickLogin('admin')"
            class="text-caption"
          >
            <v-icon start size="16">mdi-shield-account</v-icon>
            Admin
          </v-btn>
        </v-col>
        <v-col cols="4">
          <v-btn
            variant="outlined"
            size="small"
            block
            :disabled="loading"
            @click="quickLogin('moderator')"
            class="text-caption"
          >
            <v-icon start size="16">mdi-account-cog</v-icon>
            Mod
          </v-btn>
        </v-col>
        <v-col cols="4">
          <v-btn
            variant="outlined"
            size="small"
            block
            :disabled="loading"
            @click="quickLogin('user')"
            class="text-caption"
          >
            <v-icon start size="16">mdi-account</v-icon>
            User
          </v-btn>
        </v-col>
      </v-row>
    </div>

    <!-- Error Alert -->
    <v-alert
      v-if="errorMessage"
      type="error"
      variant="tonal"
      class="mt-4"
      closable
      @click:close="errorMessage = ''"
    >
      <v-icon start>mdi-alert-circle</v-icon>
      {{ errorMessage }}
    </v-alert>

    <!-- Success Alert -->
    <v-alert
      v-if="successMessage"
      type="success"
      variant="tonal"
      class="mt-4"
      closable
      @click:close="successMessage = ''"
    >
      <v-icon start>mdi-check-circle</v-icon>
      {{ successMessage }}
    </v-alert>
  </v-form>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuth } from '@/composables/useAuth'

// Emits
const emit = defineEmits(['success', 'error'])

// Composables
const { login } = useAuth()

// Form data
const form = ref(null)
const valid = ref(false)
const loading = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)

// Form fields
const email = ref('')
const password = ref('')

// Messages
const errorMessage = ref('')
const successMessage = ref('')

// Validation rules
const emailRules = [
  v => !!v || 'Email is required',
  v => /.+@.+\..+/.test(v) || 'Email must be valid'
]

const passwordRules = [
  v => !!v || 'Password is required',
  v => (v && v.length >= 6) || 'Password must be at least 6 characters'
]

// Quick login credentials
const quickLoginCredentials = {
  admin: {
    email: '<EMAIL>',
    password: 'admin123'
  },
  moderator: {
    email: '<EMAIL>',
    password: 'mod123'
  },
  user: {
    email: '<EMAIL>',
    password: 'user123'
  }
}

// Methods
const handleSubmit = async () => {
  if (!valid.value) return

  loading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const credentials = {
      email: email.value,
      password: password.value,
      rememberMe: rememberMe.value
    }

    const user = await login(credentials)
    
    successMessage.value = 'Login successful!'
    emit('success', user)
    
  } catch (error) {
    errorMessage.value = error.message || 'Login failed. Please check your credentials.'
    emit('error', error)
  } finally {
    loading.value = false
  }
}

const quickLogin = async (role) => {
  const credentials = quickLoginCredentials[role]
  if (!credentials) return

  email.value = credentials.email
  password.value = credentials.password
  rememberMe.value = true

  // Validate form
  await form.value?.validate()
  
  // Submit if valid
  if (valid.value) {
    await handleSubmit()
  }
}

const handleForgotPassword = () => {
  // TODO: Implement forgot password functionality
  console.log('Forgot password clicked')
  
  // For now, show a simple alert
  errorMessage.value = ''
  successMessage.value = 'Forgot password functionality will be implemented soon.'
}

// Clear messages when form fields change
const clearMessages = () => {
  errorMessage.value = ''
  successMessage.value = ''
}

// Watch for field changes to clear messages
const watchFields = () => {
  return [email, password]
}

watchFields().forEach(field => {
  field.value && clearMessages()
})
</script>

<style scoped>
.login-form {
  width: 100%;
}

.quick-login-section {
  margin-top: 16px;
}

/* Custom styling for form elements */
.v-text-field {
  margin-bottom: 8px;
}

.v-btn {
  text-transform: none;
}

/* Loading state styling */
.v-text-field--loading .v-field__input {
  opacity: 0.7;
}

/* Error state styling */
.v-text-field--error .v-field {
  border-color: rgb(var(--v-theme-error));
}

/* Success state styling */
.v-alert--variant-tonal.v-alert--type-success {
  background-color: rgba(var(--v-theme-success), 0.12);
  color: rgb(var(--v-theme-success));
}

.v-alert--variant-tonal.v-alert--type-error {
  background-color: rgba(var(--v-theme-error), 0.12);
  color: rgb(var(--v-theme-error));
}

/* Animation for alerts */
.v-alert {
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .quick-login-section .v-btn {
    font-size: 0.7rem;
    padding: 0 8px;
  }
  
  .quick-login-section .v-icon {
    font-size: 14px;
  }
}

/* Dark theme adjustments */
:global(.v-theme--dark) .login-form {
  color: rgb(var(--v-theme-on-surface));
}

:global(.v-theme--dark) .v-text-field .v-field {
  background-color: rgba(var(--v-theme-surface), 0.8);
}

/* Focus states */
.v-text-field .v-field--focused {
  box-shadow: 0 0 0 2px rgba(var(--v-theme-primary), 0.2);
}

/* Hover states */
.v-btn:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

.v-btn--variant-outlined:hover {
  background-color: rgba(var(--v-theme-primary), 0.04);
}
</style>
