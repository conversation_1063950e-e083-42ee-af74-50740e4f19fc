<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="$slots.fallback">
    <slot name="fallback" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useAuth } from '@/composables/useAuth'

const props = defineProps({
  // Action to check (e.g., 'read', 'create', 'update', 'delete', 'manage')
  action: {
    type: String,
    required: false
  },

  // Subject/resource to check (e.g., 'User', 'Post', 'AdminPanel')
  subject: {
    type: String,
    required: false
  },

  // Optional field or conditions to check
  field: {
    type: [String, Object],
    default: null
  },

  // Role-based access (alternative to action/subject)
  role: {
    type: [String, Array],
    default: null
  },

  // Require authentication
  requireAuth: {
    type: Boolean,
    default: false
  },

  // Invert the check (show when user CANNOT access)
  not: {
    type: Boolean,
    default: false
  }
})

const { can, isAuthenticated, userRole } = useAuth()

const hasAccess = computed(() => {
  // If only requireAuth is specified
  if (props.requireAuth && !props.role && !props.action) {
    const hasAuth = isAuthenticated.value
    return props.not ? !hasAuth : hasAuth
  }

  // Role-based check
  if (props.role) {
    const roles = Array.isArray(props.role) ? props.role : [props.role]
    const hasRole = roles.includes(userRole.value)
    return props.not ? !hasRole : hasRole
  }

  // Permission-based check (requires both action and subject)
  if (props.action && props.subject) {
    const hasPermission = can(props.action, props.subject, props.field)
    return props.not ? !hasPermission : hasPermission
  }

  // If no valid check criteria provided, deny access
  return props.not ? true : false
})
</script>

<style scoped>
/* Component has no visual styling - it's purely functional */
</style>
