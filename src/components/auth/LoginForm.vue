<template>
  <div class="login-form-container">
    <div class="card">
      <div class="card-header">
        <h3 class="card-title">Login</h3>
        <p class="text-muted">Sign in to your account</p>
      </div>
      
      <div class="card-body">
        <!-- Error <PERSON> -->
        <div v-if="error" class="alert alert-danger" role="alert">
          <i class="icon-alert-circle"></i>
          {{ error }}
          <button 
            type="button" 
            class="btn-close" 
            @click="clearError"
            aria-label="Close"
          ></button>
        </div>

        <!-- Demo Accounts Info -->
        <div class="alert alert-info mb-3">
          <h6>Demo Accounts:</h6>
          <small class="d-block">Admin: <EMAIL> / admin123</small>
          <small class="d-block">Moderator: <EMAIL> / mod123</small>
          <small class="d-block">User: <EMAIL> / user123</small>
        </div>

        <form @submit.prevent="handleSubmit">
          <!-- Email Field -->
          <div class="mb-3">
            <label for="email" class="form-label">Email</label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="form-control"
              :class="{ 'is-invalid': emailError }"
              placeholder="Enter your email"
              required
              :disabled="isLoading"
            />
            <div v-if="emailError" class="invalid-feedback">
              {{ emailError }}
            </div>
          </div>

          <!-- Password Field -->
          <div class="mb-3">
            <label for="password" class="form-label">Password</label>
            <div class="input-group">
              <input
                id="password"
                v-model="form.password"
                :type="showPassword ? 'text' : 'password'"
                class="form-control"
                :class="{ 'is-invalid': passwordError }"
                placeholder="Enter your password"
                required
                :disabled="isLoading"
              />
              <button
                type="button"
                class="btn btn-outline-secondary"
                @click="togglePassword"
                :disabled="isLoading"
              >
                <i :class="showPassword ? 'icon-eye-off' : 'icon-eye'"></i>
              </button>
            </div>
            <div v-if="passwordError" class="invalid-feedback">
              {{ passwordError }}
            </div>
          </div>

          <!-- Remember Me -->
          <div class="mb-3 form-check">
            <input
              id="remember"
              v-model="form.remember"
              type="checkbox"
              class="form-check-input"
              :disabled="isLoading"
            />
            <label for="remember" class="form-check-label">
              Remember me
            </label>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            class="btn btn-primary w-100"
            :disabled="isLoading || !isFormValid"
          >
            <span v-if="isLoading" class="spinner-border spinner-border-sm me-2"></span>
            {{ isLoading ? 'Signing in...' : 'Sign In' }}
          </button>
        </form>

        <!-- Quick Login Buttons -->
        <div class="mt-3">
          <p class="text-center text-muted">Quick Login:</p>
          <div class="d-grid gap-2">
            <button 
              class="btn btn-outline-primary btn-sm"
              @click="quickLogin('admin')"
              :disabled="isLoading"
            >
              Login as Admin
            </button>
            <button 
              class="btn btn-outline-secondary btn-sm"
              @click="quickLogin('moderator')"
              :disabled="isLoading"
            >
              Login as Moderator
            </button>
            <button 
              class="btn btn-outline-info btn-sm"
              @click="quickLogin('user')"
              :disabled="isLoading"
            >
              Login as User
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useAuth } from '@/composables/useAuth'
import { useRouter } from 'vue-router'

const emit = defineEmits(['success', 'error'])

const { login, isLoading, error, clearError } = useAuth()
const router = useRouter()

// Form state
const form = ref({
  email: '',
  password: '',
  remember: false
})

const showPassword = ref(false)

// Validation
const emailError = ref('')
const passwordError = ref('')

const isFormValid = computed(() => {
  return form.value.email && form.value.password && !emailError.value && !passwordError.value
})

// Methods
const validateEmail = () => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!form.value.email) {
    emailError.value = 'Email is required'
  } else if (!emailRegex.test(form.value.email)) {
    emailError.value = 'Please enter a valid email'
  } else {
    emailError.value = ''
  }
}

const validatePassword = () => {
  if (!form.value.password) {
    passwordError.value = 'Password is required'
  } else if (form.value.password.length < 6) {
    passwordError.value = 'Password must be at least 6 characters'
  } else {
    passwordError.value = ''
  }
}

const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const handleSubmit = async () => {
  validateEmail()
  validatePassword()

  if (!isFormValid.value) return

  const result = await login({
    email: form.value.email,
    password: form.value.password
  })

  if (result.success) {
    emit('success', result.user)
    router.push('/')
  } else {
    emit('error', result.error)
  }
}

const quickLogin = async (role) => {
  const credentials = {
    admin: { email: '<EMAIL>', password: 'admin123' },
    moderator: { email: '<EMAIL>', password: 'mod123' },
    user: { email: '<EMAIL>', password: 'user123' }
  }

  form.value.email = credentials[role].email
  form.value.password = credentials[role].password
  
  await handleSubmit()
}
</script>

<style scoped>
.login-form-container {
  max-width: 400px;
  margin: 2rem auto;
  padding: 1rem;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border-radius: 0.5rem;
}

.card-header {
  background: transparent;
  border-bottom: 1px solid #dee2e6;
  text-align: center;
  padding: 1.5rem 1.5rem 1rem;
}

.card-title {
  margin-bottom: 0.5rem;
  color: var(--Primary, #161326);
}

.btn-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  opacity: 0.5;
}

.btn-close:hover {
  opacity: 1;
}

.input-group .btn {
  border-left: none;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}
</style>
