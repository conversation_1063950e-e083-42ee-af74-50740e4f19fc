<template>
  <div class="line-fix">
    <h3>Line Chart Fix Tests</h3>
    
    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>Fixed Line Chart (Non-stacked)</h4>
          <ApexChart
            chart-id="fixed-line-unstacked"
            chart-type="line-chart-twoline"
            :options="fixedOptionsUnstacked"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>Fixed Line Chart (Better Colors)</h4>
          <ApexChart
            chart-id="fixed-line-colors"
            chart-type="line-chart-twoline"
            :options="fixedOptionsColors"
          />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>Fixed Line Chart (No Negative Values)</h4>
          <ApexChart
            chart-id="fixed-line-positive"
            :series="positiveDataSeries"
            :options="fixedOptionsPositive"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>Working Line Chart (Simple)</h4>
          <ApexChart
            chart-id="simple-line"
            :series="simpleSeries"
            :options="simpleOptions"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'

const fixedOptionsUnstacked = ref({
  chart: { 
    height: 300, 
    stacked: false,
    toolbar: { show: true }
  },
  legend: { show: true },
  colors: ['#D4FE75', '#FF6B6B'],
  stroke: { width: 3 },
  tooltip: { enabled: true },
  yaxis: { show: true }
})

const fixedOptionsColors = ref({
  chart: { 
    height: 300, 
    stacked: true,
    toolbar: { show: true }
  },
  legend: { show: true },
  colors: ['#D4FE75', '#FF6B6B'], // More visible colors
  stroke: { width: 2 },
  tooltip: { enabled: true },
  yaxis: { show: true }
})

const positiveDataSeries = ref([
  {
    name: 'Item 01',
    data: [31, 90, 58, 70, 92, 89, 80],
  },
  {
    name: 'Item 02',
    data: [51, 45, 25, 51, 34, 42, 41], // Removed negative value
  },
])

const fixedOptionsPositive = ref({
  chart: { 
    height: 300, 
    type: 'line',
    stacked: true,
    toolbar: { show: true }
  },
  legend: { show: true },
  colors: ['#D4FE75', '#FF6B6B'],
  stroke: { 
    curve: 'smooth',
    width: 2 
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  },
  tooltip: { enabled: true },
  yaxis: { show: true }
})

const simpleSeries = ref([
  {
    name: 'Series 1',
    data: [10, 20, 30, 40, 50, 60, 70],
  },
  {
    name: 'Series 2',
    data: [15, 25, 35, 45, 55, 65, 75],
  },
])

const simpleOptions = ref({
  chart: { 
    height: 300, 
    type: 'line',
    toolbar: { show: true }
  },
  legend: { show: true },
  colors: ['#008FFB', '#00E396'],
  stroke: { 
    curve: 'smooth',
    width: 3 
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  },
  tooltip: { enabled: true }
})
</script>

<style scoped>
.line-fix {
  padding: 20px;
}

.test-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.test-item h4 {
  margin-top: 0;
  color: #333;
}
</style>
