<template>
  <div class="background-demo">
    <h3>🎨 Chart Background Color Examples</h3>
    
    <div class="row">
      <div class="col-md-6">
        <div class="demo-item">
          <h4>Light Gray Background</h4>
          <ApexChart
            chart-id="bg-light-gray"
            chart-type="line-chart-twoline-improved"
            :options="lightGrayOptions"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="demo-item">
          <h4>Dark Background</h4>
          <ApexChart
            chart-id="bg-dark"
            chart-type="line-chart-twoline-improved"
            :options="darkOptions"
          />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="demo-item">
          <h4>Blue Gradient Background</h4>
          <ApexChart
            chart-id="bg-blue-gradient"
            chart-type="line-chart-twoline-improved"
            :options="blueGradientOptions"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="demo-item">
          <h4>Custom Color Background</h4>
          <ApexChart
            chart-id="bg-custom"
            chart-type="line-chart-twoline-improved"
            :options="customOptions"
          />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="demo-item">
          <h4>Transparent Background</h4>
          <ApexChart
            chart-id="bg-transparent"
            chart-type="line-chart-twoline-improved"
            :options="transparentOptions"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="demo-item">
          <h4>Interactive Color Picker</h4>
          <div class="color-picker-section">
            <label for="colorPicker">Choose Background Color:</label>
            <input 
              id="colorPicker" 
              type="color" 
              v-model="selectedColor" 
              class="form-control color-input"
            />
            <p class="mt-2">Selected: {{ selectedColor }}</p>
          </div>
          <ApexChart
            chart-id="bg-interactive"
            chart-type="line-chart-twoline-improved"
            :options="interactiveOptions"
          />
        </div>
      </div>
    </div>
    
    <div class="code-examples">
      <h4>📝 Code Examples</h4>
      <div class="code-block">
        <h5>Basic Background Color:</h5>
        <pre><code>&lt;ApexChart
  chart-type="line-chart-twoline-improved"
  :options="{
    chart: { 
      background: '#f8f9fa' // Light gray
    }
  }"
/&gt;</code></pre>
      </div>
      
      <div class="code-block">
        <h5>Dark Theme with White Text:</h5>
        <pre><code>&lt;ApexChart
  chart-type="line-chart-twoline-improved"
  :options="{
    chart: { 
      background: '#1a1a1a' // Dark background
    },
    theme: {
      mode: 'dark'
    },
    xaxis: {
      labels: { style: { colors: '#ffffff' } }
    },
    yaxis: {
      labels: { style: { colors: '#ffffff' } }
    }
  }"
/&gt;</code></pre>
      </div>
      
      <div class="code-block">
        <h5>Gradient Background:</h5>
        <pre><code>&lt;ApexChart
  chart-type="line-chart-twoline-improved"
  :options="{
    chart: { 
      background: 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)'
    }
  }"
/&gt;</code></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'

const selectedColor = ref('#e3f2fd')

const lightGrayOptions = ref({
  chart: { 
    height: 250,
    background: '#f8f9fa'
  },
  title: { text: 'Light Gray (#f8f9fa)' }
})

const darkOptions = ref({
  chart: { 
    height: 250,
    background: '#1a1a1a'
  },
  theme: {
    mode: 'dark'
  },
  title: { 
    text: 'Dark Theme (#1a1a1a)',
    style: { color: '#ffffff' }
  },
  xaxis: {
    labels: { style: { colors: '#ffffff' } }
  },
  yaxis: {
    labels: { style: { colors: '#ffffff' } }
  }
})

const blueGradientOptions = ref({
  chart: { 
    height: 250,
    background: 'linear-gradient(45deg, #667eea 0%, #764ba2 100%)'
  },
  title: { 
    text: 'Blue Gradient',
    style: { color: '#ffffff' }
  },
  xaxis: {
    labels: { style: { colors: '#ffffff' } }
  },
  yaxis: {
    labels: { style: { colors: '#ffffff' } }
  }
})

const customOptions = ref({
  chart: { 
    height: 250,
    background: '#fff3e0' // Light orange
  },
  title: { text: 'Custom Orange (#fff3e0)' }
})

const transparentOptions = ref({
  chart: { 
    height: 250,
    background: 'transparent'
  },
  title: { text: 'Transparent Background' }
})

const interactiveOptions = computed(() => ({
  chart: { 
    height: 250,
    background: selectedColor.value
  },
  title: { text: `Dynamic (${selectedColor.value})` }
}))
</script>

<style scoped>
.background-demo {
  padding: 20px;
}

.demo-item {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.demo-item h4 {
  margin-top: 0;
  color: #333;
}

.color-picker-section {
  margin-bottom: 15px;
}

.color-input {
  width: 100px;
  height: 40px;
  padding: 5px;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.code-examples {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.code-block {
  margin-bottom: 20px;
}

.code-block h5 {
  color: #495057;
  margin-bottom: 10px;
}

.code-block pre {
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  overflow-x: auto;
}

.code-block code {
  color: #495057;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}
</style>
