<template>
  <div class="color-card-test">
    <h1>ColorCard Component Test</h1>
    
    <!-- Test Controls -->
    <div class="test-controls mb-4">
      <h3>Test Controls</h3>
      <div class="row">
        <div class="col-md-3">
          <label>Background:</label>
          <select v-model="testConfig.background" class="form-control">
            <option value="bg-1">bg-1</option>
            <option value="bg-2">bg-2</option>
            <option value="bg-3">bg-3</option>
            <option value="bg-4">bg-4</option>
          </select>
        </div>
        <div class="col-md-3">
          <label>Wallet Name:</label>
          <input v-model="testConfig.walletName" class="form-control" />
        </div>
        <div class="col-md-3">
          <label>Amount:</label>
          <input v-model="testConfig.amount" class="form-control" type="number" step="0.01" />
        </div>
        <div class="col-md-3">
          <label>Card Number:</label>
          <input v-model="testConfig.cardNumber" class="form-control" />
        </div>
      </div>
      
      <div class="row mt-3">
        <div class="col-md-3">
          <label>
            <input type="checkbox" v-model="testConfig.clickable" class="form-check-input me-2">
            Clickable
          </label>
        </div>
        <div class="col-md-3">
          <label>
            <input type="checkbox" v-model="testConfig.disabled" class="form-check-input me-2">
            Disabled
          </label>
        </div>
        <div class="col-md-3">
          <label>
            <input type="checkbox" v-model="testConfig.showIcon" class="form-check-input me-2">
            Show Icon
          </label>
        </div>
        <div class="col-md-3">
          <label>
            <input type="checkbox" v-model="testConfig.useCustomIcon" class="form-check-input me-2">
            Use Custom Icon
          </label>
        </div>
      </div>
      
      <div class="mt-3">
        <button @click="randomizeCard" class="btn btn-primary me-2">Randomize Card</button>
        <button @click="resetCard" class="btn btn-secondary me-2">Reset Card</button>
        <button @click="addToCollection" class="btn btn-success">Add to Collection</button>
      </div>
    </div>
    
    <!-- Live Preview -->
    <div class="live-preview mb-4">
      <h3>Live Preview</h3>
      <div class="preview-container">
        <ColorCard
          :wallet-name="testConfig.walletName"
          :amount="testConfig.amount"
          :card-number="testConfig.cardNumber"
          :background="testConfig.background"
          :clickable="testConfig.clickable"
          :disabled="testConfig.disabled"
          :show-icon="testConfig.showIcon"
          :custom-icon="testConfig.useCustomIcon ? customIconSvg : null"
          @click="logEvent('click', $event)"
          @hover="logEvent('hover', $event)"
          @focus="logEvent('focus', $event)"
        />
      </div>
    </div>
    
    <!-- Background Showcase -->
    <div class="background-showcase mb-4">
      <h3>All Background Options</h3>
      <div class="row">
        <div class="col-md-3" v-for="bg in backgrounds" :key="bg">
          <div class="bg-preview">
            <h5>{{ bg }}</h5>
            <ColorCard
              :wallet-name="`${bg} Wallet`"
              :amount="Math.floor(Math.random() * 100000)"
              card-number="**** **** **** 1234"
              :background="bg"
              :clickable="true"
              @click="logEvent('background-click', { background: bg, ...$event })"
            />
          </div>
        </div>
      </div>
    </div>
    
    <!-- Custom Slot Examples -->
    <div class="slot-examples mb-4">
      <h3>Custom Slot Examples</h3>
      <div class="row">
        <div class="col-md-6">
          <h5>Custom Icon Slot</h5>
          <ColorCard
            wallet-name="Premium Account"
            :amount="99999.99"
            card-number="1111 2222 3333 4444"
            background="bg-1"
            :clickable="true"
            @click="logEvent('premium-click', $event)"
          >
            <template #icon>
              <div class="premium-icon">
                ⭐
              </div>
            </template>
          </ColorCard>
        </div>
        
        <div class="col-md-6">
          <h5>Custom Content Slots</h5>
          <ColorCard
            wallet-name="Business Account"
            :amount="250000"
            card-number="5555 6666 7777 8888"
            background="bg-2"
            :clickable="true"
            @click="logEvent('business-click', $event)"
          >
            <template #walletName="{ walletName }">
              <div class="custom-wallet">
                🏢 {{ walletName }}
              </div>
            </template>
            
            <template #amount="{ formattedAmount }">
              <div class="custom-amount">
                {{ formattedAmount }}
                <small class="growth">+5.2%</small>
              </div>
            </template>
            
            <template #additional>
              <div class="business-info">
                <small>Tax Year: 2024</small>
              </div>
            </template>
          </ColorCard>
        </div>
      </div>
    </div>
    
    <!-- Card Collection -->
    <div class="card-collection mb-4" v-if="cardCollection.length > 0">
      <h3>Card Collection ({{ cardCollection.length }} cards)</h3>
      <div class="collection-grid">
        <div v-for="(card, index) in cardCollection" :key="index" class="collection-item">
          <ColorCard
            :wallet-name="card.walletName"
            :amount="card.amount"
            :card-number="card.cardNumber"
            :background="card.background"
            :clickable="true"
            @click="logEvent('collection-click', { index, ...$event })"
          />
          <button @click="removeFromCollection(index)" class="btn btn-sm btn-danger mt-2">
            Remove
          </button>
        </div>
      </div>
      <button @click="clearCollection" class="btn btn-outline-danger mt-3">Clear Collection</button>
    </div>
    
    <!-- Event Log -->
    <div class="event-log">
      <h3>Event Log</h3>
      <div class="log-container">
        <div v-for="(event, index) in eventLog" :key="index" class="log-entry">
          <span class="timestamp">{{ event.timestamp }}</span>
          <span class="event-type">{{ event.type }}</span>
          <span class="event-data">{{ JSON.stringify(event.data) }}</span>
        </div>
      </div>
      <button @click="clearLog" class="btn btn-sm btn-outline-secondary">Clear Log</button>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import ColorCard from '@/components/shared/ColorCard.vue'

// Test configuration
const testConfig = reactive({
  walletName: 'Test Wallet',
  amount: 12345.67,
  cardNumber: '1234 5678 9012 3456',
  background: 'bg-1',
  clickable: true,
  disabled: false,
  showIcon: true,
  useCustomIcon: false
})

// Available backgrounds
const backgrounds = ['bg-1', 'bg-2', 'bg-3', 'bg-4']

// Custom icon SVG
const customIconSvg = `
  <svg width="35" height="22" viewBox="0 0 35 22" fill="none">
    <rect width="35" height="22" rx="4" fill="#FFD700"/>
    <text x="17.5" y="14" text-anchor="middle" fill="#333" font-size="10" font-weight="bold">★</text>
  </svg>
`

// Card collection
const cardCollection = ref([])

// Event logging
const eventLog = ref([])

function logEvent(type, data) {
  eventLog.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    data
  })
  
  // Keep only last 20 events
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

function clearLog() {
  eventLog.value = []
}

// Test functions
function randomizeCard() {
  const walletNames = ['Main Wallet', 'Savings Account', 'Business Fund', 'Emergency Fund', 'Investment Portfolio']
  const cardPrefixes = ['4532', '5555', '6011', '3782', '6219']
  
  testConfig.walletName = walletNames[Math.floor(Math.random() * walletNames.length)]
  testConfig.amount = Math.floor(Math.random() * 100000) + Math.random()
  testConfig.background = backgrounds[Math.floor(Math.random() * backgrounds.length)]
  
  const prefix = cardPrefixes[Math.floor(Math.random() * cardPrefixes.length)]
  const randomNumbers = Array.from({length: 3}, () => 
    Math.floor(Math.random() * 10000).toString().padStart(4, '0')
  ).join(' ')
  testConfig.cardNumber = `${prefix} ${randomNumbers}`
  
  logEvent('randomize', { ...testConfig })
}

function resetCard() {
  Object.assign(testConfig, {
    walletName: 'Test Wallet',
    amount: 12345.67,
    cardNumber: '1234 5678 9012 3456',
    background: 'bg-1',
    clickable: true,
    disabled: false,
    showIcon: true,
    useCustomIcon: false
  })
  
  logEvent('reset', { ...testConfig })
}

function addToCollection() {
  cardCollection.value.push({ ...testConfig })
  logEvent('add-to-collection', { ...testConfig })
}

function removeFromCollection(index) {
  const removed = cardCollection.value.splice(index, 1)[0]
  logEvent('remove-from-collection', { index, card: removed })
}

function clearCollection() {
  const count = cardCollection.value.length
  cardCollection.value = []
  logEvent('clear-collection', { removedCount: count })
}
</script>

<style scoped>
.color-card-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.live-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.preview-container {
  display: flex;
  justify-content: center;
  padding: 20px;
}

.background-showcase {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.bg-preview {
  text-align: center;
  margin-bottom: 20px;
}

.bg-preview h5 {
  margin-bottom: 15px;
  color: #666;
  text-transform: uppercase;
  font-size: 14px;
}

.slot-examples {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.premium-icon {
  font-size: 24px;
  background: linear-gradient(45deg, #FFD700, #FFA500);
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-wallet {
  color: #6D6D6D;
  font-size: 12px;
  font-weight: 500;
}

.custom-amount {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.growth {
  color: #2BC155;
  font-weight: bold;
}

.business-info {
  margin-top: 8px;
}

.business-info small {
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 4px;
  color: #fff;
}

.card-collection {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.collection-item {
  text-align: center;
}

.event-log {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  background: white;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.log-entry {
  display: block;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.timestamp {
  color: #666;
  margin-right: 10px;
}

.event-type {
  color: #007bff;
  font-weight: bold;
  margin-right: 10px;
}

.event-data {
  color: #333;
}

.form-control, .form-check-input {
  margin-top: 5px;
}

.btn {
  margin-right: 5px;
}
</style>
