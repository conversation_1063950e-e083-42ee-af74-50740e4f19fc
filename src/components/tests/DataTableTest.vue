<template>
  <div class="data-table-test">
    <h1>DataTable Component Test</h1>
    
    <!-- Test Controls -->
    <div class="test-controls mb-4">
      <h3>Test Controls</h3>
      <div class="row">
        <div class="col-md-3">
          <label>Items per page:</label>
          <select v-model="itemsPerPage" class="form-control">
            <option value="3">3</option>
            <option value="5">5</option>
            <option value="10">10</option>
          </select>
        </div>
        <div class="col-md-3">
          <label>Enable Search:</label>
          <input type="checkbox" v-model="searchable" class="form-check-input">
        </div>
        <div class="col-md-3">
          <label>Enable Sorting:</label>
          <input type="checkbox" v-model="sortable" class="form-check-input">
        </div>
        <div class="col-md-3">
          <label>Enable Selection:</label>
          <input type="checkbox" v-model="selectable" class="form-check-input">
        </div>
      </div>
      
      <div class="mt-3">
        <button @click="addRandomData" class="btn btn-primary me-2">Add Random Data</button>
        <button @click="clearData" class="btn btn-danger me-2">Clear Data</button>
        <button @click="resetData" class="btn btn-secondary">Reset Data</button>
      </div>
    </div>
    
    <!-- Event Log -->
    <div class="event-log mb-4">
      <h3>Event Log</h3>
      <div class="log-container">
        <div v-for="(event, index) in eventLog" :key="index" class="log-entry">
          <span class="timestamp">{{ event.timestamp }}</span>
          <span class="event-type">{{ event.type }}</span>
          <span class="event-data">{{ JSON.stringify(event.data) }}</span>
        </div>
      </div>
      <button @click="clearLog" class="btn btn-sm btn-outline-secondary">Clear Log</button>
    </div>
    
    <!-- DataTable Test -->
    <div class="table-test">
      <h3>DataTable Component</h3>
      <DataTable
        :data="testData"
        :columns="testColumns"
        title="Test Data Table"
        :searchable="searchable"
        :sortable="sortable"
        :selectable="selectable"
        :pagination="true"
        :items-per-page="itemsPerPage"
        @row-select="logEvent('row-select', $event)"
        @row-click="logEvent('row-click', $event)"
        @sort-change="logEvent('sort-change', $event)"
        @search="logEvent('search', $event)"
        @export-data="logEvent('export-data', $event)"
      >
        <!-- Custom status column -->
        <template #column-status="{ value, row }">
          <div class="custom-status" :class="getStatusClass(value)">
            <i v-if="value === 'active'" class="icon-check"></i>
            {{ value.toUpperCase() }}
          </div>
        </template>
        
        <!-- Custom actions column -->
        <template #column-actions="{ row }">
          <button 
            @click="editRow(row)" 
            class="btn btn-sm btn-outline-primary me-1"
          >
            Edit
          </button>
          <button 
            @click="deleteRow(row)" 
            class="btn btn-sm btn-outline-danger"
          >
            Delete
          </button>
        </template>
        
        <!-- Empty state -->
        <template #empty-state>
          <div class="text-center py-4">
            <h4>No data available</h4>
            <p>Click "Add Random Data" to populate the table</p>
          </div>
        </template>
      </DataTable>
    </div>
    
    <!-- Data Preview -->
    <div class="data-preview mt-4">
      <h3>Current Data ({{ testData.length }} items)</h3>
      <pre class="data-json">{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import DataTable from '@/components/shared/DataTable.vue'

// Test configuration
const itemsPerPage = ref(5)
const searchable = ref(true)
const sortable = ref(true)
const selectable = ref(true)

// Event logging
const eventLog = ref([])

function logEvent(type, data) {
  eventLog.value.unshift({
    timestamp: new Date().toLocaleTimeString(),
    type,
    data
  })
  
  // Keep only last 20 events
  if (eventLog.value.length > 20) {
    eventLog.value = eventLog.value.slice(0, 20)
  }
}

function clearLog() {
  eventLog.value = []
}

// Test data
const initialData = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    age: 30,
    department: 'Engineering',
    salary: 75000,
    status: 'active',
    joinDate: '2023-01-15'
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    age: 28,
    department: 'Marketing',
    salary: 65000,
    status: 'active',
    joinDate: '2023-03-20'
  },
  {
    id: 3,
    name: 'Bob Johnson',
    email: '<EMAIL>',
    age: 35,
    department: 'Sales',
    salary: 70000,
    status: 'inactive',
    joinDate: '2022-11-10'
  },
  {
    id: 4,
    name: 'Alice Brown',
    email: '<EMAIL>',
    age: 32,
    department: 'HR',
    salary: 60000,
    status: 'active',
    joinDate: '2023-05-08'
  }
]

const testData = ref([...initialData])

// Column configuration
const testColumns = ref([
  {
    key: 'name',
    label: 'Name',
    sortable: true
  },
  {
    key: 'email',
    label: 'Email',
    sortable: true
  },
  {
    key: 'age',
    label: 'Age',
    sortable: true
  },
  {
    key: 'department',
    label: 'Department',
    sortable: true
  },
  {
    key: 'salary',
    label: 'Salary',
    type: 'currency',
    sortable: true
  },
  {
    key: 'status',
    label: 'Status',
    sortable: true
  },
  {
    key: 'joinDate',
    label: 'Join Date',
    type: 'date',
    format: 'short',
    sortable: true
  },
  {
    key: 'actions',
    label: 'Actions',
    sortable: false
  }
])

// Test functions
function addRandomData() {
  const names = ['Charlie Wilson', 'Diana Prince', 'Eve Adams', 'Frank Miller', 'Grace Lee']
  const departments = ['Engineering', 'Marketing', 'Sales', 'HR', 'Finance']
  const statuses = ['active', 'inactive', 'pending']
  
  const randomName = names[Math.floor(Math.random() * names.length)]
  const randomDept = departments[Math.floor(Math.random() * departments.length)]
  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
  
  const newItem = {
    id: Date.now(),
    name: randomName,
    email: randomName.toLowerCase().replace(' ', '.') + '@example.com',
    age: Math.floor(Math.random() * 40) + 25,
    department: randomDept,
    salary: Math.floor(Math.random() * 50000) + 50000,
    status: randomStatus,
    joinDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  }
  
  testData.value.push(newItem)
  logEvent('data-added', newItem)
}

function clearData() {
  testData.value = []
  logEvent('data-cleared', {})
}

function resetData() {
  testData.value = [...initialData]
  logEvent('data-reset', {})
}

function editRow(row) {
  logEvent('edit-clicked', row)
  alert(`Edit row: ${row.name}`)
}

function deleteRow(row) {
  logEvent('delete-clicked', row)
  if (confirm(`Delete ${row.name}?`)) {
    const index = testData.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      testData.value.splice(index, 1)
      logEvent('row-deleted', row)
    }
  }
}

function getStatusClass(status) {
  switch (status) {
    case 'active':
      return 'status-active'
    case 'inactive':
      return 'status-inactive'
    case 'pending':
      return 'status-pending'
    default:
      return ''
  }
}
</script>

<style scoped>
.data-table-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.event-log {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.log-entry {
  display: block;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.timestamp {
  color: #666;
  margin-right: 10px;
}

.event-type {
  color: #007bff;
  font-weight: bold;
  margin-right: 10px;
}

.event-data {
  color: #333;
}

.data-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.data-json {
  background: white;
  padding: 15px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
}

.custom-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: bold;
}

.status-active {
  background-color: #d4edda;
  color: #155724;
}

.status-inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.form-control, .form-check-input {
  margin-top: 5px;
}

.btn {
  margin-right: 5px;
}
</style>
