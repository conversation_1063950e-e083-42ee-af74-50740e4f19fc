<template>
  <div class="line-debug">
    <h3>Line Chart Two-line Debug</h3>
    
    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>Raw Configuration</h4>
          <pre class="config-display">{{ JSON.stringify(rawConfig, null, 2) }}</pre>
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>Final Configuration (Dynamic)</h4>
          <pre class="config-display">{{ JSON.stringify(finalConfig, null, 2) }}</pre>
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>Dynamic Line Chart (Original Config)</h4>
          <ApexChart
            chart-id="debug-line-original"
            chart-type="line-chart-twoline"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>Dynamic Line Chart (With Height Override)</h4>
          <ApexChart
            chart-id="debug-line-height"
            chart-type="line-chart-twoline"
            :options="{ chart: { height: 300 } }"
          />
        </div>
      </div>
    </div>
    
    <div class="row">
      <div class="col-md-6">
        <div class="test-item">
          <h4>Dynamic Line Chart (Non-stacked)</h4>
          <ApexChart
            chart-id="debug-line-unstacked"
            chart-type="line-chart-twoline"
            :options="{ 
              chart: { height: 300, stacked: false },
              legend: { show: true },
              tooltip: { enabled: true }
            }"
          />
        </div>
      </div>
      <div class="col-md-6">
        <div class="test-item">
          <h4>Manual Line Chart (Working Reference)</h4>
          <ApexChart
            chart-id="debug-line-manual"
            :series="manualSeries"
            :options="manualOptions"
          />
        </div>
      </div>
    </div>
    
    <div class="test-item">
      <h4>Raw ApexCharts Test</h4>
      <div id="raw-line-chart"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import ApexChart from '@/components/shared/ApexChart.vue'
import { getChartConfig } from '@/plugins/charts.js'
import ApexCharts from 'apexcharts'

const rawConfig = computed(() => getChartConfig('line-chart-twoline'))

const finalConfig = computed(() => {
  const base = rawConfig.value || {}
  return {
    ...base,
    chart: { ...base.chart, height: 300 }
  }
})

const manualSeries = ref([
  {
    name: 'Item 01',
    data: [31, 90, 58, 70, 92, 89, 80],
  },
  {
    name: 'Item 02', 
    data: [51, 45, -25, 51, 34, 2, 41],
  },
])

const manualOptions = ref({
  chart: {
    height: 300,
    type: 'line',
    stacked: true,
    toolbar: { show: false },
  },
  legend: { show: true },
  colors: ['#D4FE75', '#ffffff4d'],
  stroke: {
    curve: 'smooth',
    width: 2,
  },
  xaxis: {
    categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
  },
  tooltip: { enabled: true }
})

onMounted(() => {
  // Test raw ApexCharts rendering
  const config = getChartConfig('line-chart-twoline')
  if (config) {
    const chart = new ApexCharts(document.querySelector('#raw-line-chart'), {
      ...config,
      chart: { ...config.chart, height: 300 },
      legend: { show: true },
      tooltip: { enabled: true }
    })
    chart.render()
  }
})
</script>

<style scoped>
.line-debug {
  padding: 20px;
}

.test-item {
  margin-bottom: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: white;
}

.test-item h4 {
  margin-top: 0;
  color: #333;
}

.config-display {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-size: 11px;
  max-height: 300px;
  overflow-y: auto;
}

#raw-line-chart {
  height: 300px;
}
</style>
